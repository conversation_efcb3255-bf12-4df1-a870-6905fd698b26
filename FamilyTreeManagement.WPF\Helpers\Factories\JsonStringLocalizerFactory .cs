
// using System.IO;
// using Microsoft.Extensions.Hosting;
// using Microsoft.Extensions.Localization;
// using Microsoft.Extensions.Options;

// namespace FamilyTreeManagement.WPF.Helpers.Factories;

// public class JsonStringLocalizerFactory : IStringLocalizerFactory
// {
//     private readonly string _resourcesPath;
//     public JsonStringLocalizerFactory(IHostEnvironment env, IOptions<LocalizationOptions> options)
//     {
//         _resourcesPath = Path.Combine(env.ContentRootPath, options.Value.ResourcesPath);
//     }

//     public IStringLocalizer Create(Type resourceSource) =>
//         new JsonStringLocalizer(_resourcesPath, resourceSource.Name);

//     public IStringLocalizer Create(string baseName, string location) =>
//         new JsonStringLocalizer(_resourcesPath, baseName);
// }

// // 2. Register it in Program.cs
// builder.Services.AddSingleton<IStringLocalizerFactory, JsonStringLocalizerFactory>();
// builder.Services.AddLocalization(options => 
// {
//     options.ResourcesPath = "Resources/Locales";
// });