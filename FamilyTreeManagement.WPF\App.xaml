<Application x:Class="FamilyTreeManagement.WPF.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:FamilyTreeManagement.WPF.ViewModels"
             xmlns:views="clr-namespace:FamilyTreeManagement.WPF.Views"
             xmlns:converters="clr-namespace:FamilyTreeManagement.WPF.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Converters -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>

            <!-- DataTemplates for ViewModels -->
            <DataTemplate DataType="{x:Type vm:RegisterFamilyViewModel}">
                <views:RegisterFamilyView />
            </DataTemplate>
            
        </ResourceDictionary>
    </Application.Resources>
</Application>
