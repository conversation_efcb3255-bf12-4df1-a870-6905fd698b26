<Window x:Class="FamilyTreeManagement.WPF.Windows.AddPersonWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Add New Person" Height="500" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Title -->
        <TextBlock Grid.Row="0" Text="ADD NEW PERSON" 
                   FontSize="20" FontWeight="Bold" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>
        
        <!-- Form -->
        <StackPanel Grid.Row="1">
            <TextBlock Text="First Name *" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="FirstNameTextBox" Height="30" Margin="0,0,0,10"/>
            
            <TextBlock Text="Last Name *" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="LastNameTextBox" Height="30" Margin="0,0,0,10"/>
            
            <TextBlock Text="Gender *" FontWeight="Bold" Margin="0,0,0,5"/>
            <ComboBox x:Name="GenderComboBox" Height="30" Margin="0,0,0,10">
                <ComboBoxItem Content="Male" Tag="1" IsSelected="True"/>
                <ComboBoxItem Content="Female" Tag="2"/>
                <ComboBoxItem Content="Other" Tag="3"/>
            </ComboBox>
            
            <TextBlock Text="Date of Birth" FontWeight="Bold" Margin="0,0,0,5"/>
            <DatePicker x:Name="DateOfBirthPicker" Height="30" Margin="0,0,0,10"/>
            
            <TextBlock Text="Place of Birth" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="PlaceOfBirthTextBox" Height="30" Margin="0,0,0,10"/>
            
            <TextBlock Text="Occupation" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="OccupationTextBox" Height="30" Margin="0,0,0,10"/>
        </StackPanel>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Content="Clear" Click="ClearButton_Click" Width="80" Height="35" Margin="0,0,10,0"/>
            <Button Content="Add Person" Click="AddPersonButton_Click" Width="80" Height="35" Margin="0,0,10,0" Background="LightBlue"/>
            <Button Content="Close" Click="CloseButton_Click" Width="80" Height="35" Background="LightCoral"/>
        </StackPanel>
    </Grid>
</Window>
