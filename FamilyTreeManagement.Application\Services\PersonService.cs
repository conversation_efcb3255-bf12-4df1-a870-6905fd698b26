using AutoMapper;
using FamilyTreeManagement.Application.DTOs;
using FamilyTreeManagement.Domain.Entities;
using FamilyTreeManagement.Domain.Enums;
using FamilyTreeManagement.Domain.Interfaces;

namespace FamilyTreeManagement.Application.Services
{
    public interface IPersonService
    {
        Task<IEnumerable<PersonDto>> GetAllPersonsAsync();
        Task<PersonDto?> GetPersonByIdAsync(int id);
        Task<IEnumerable<PersonDto>> SearchPersonsByNameAsync(string name);
        Task<PersonDto> CreatePersonAsync(CreatePersonDto createPersonDto);
        Task<PersonDto> UpdatePersonAsync(UpdatePersonDto updatePersonDto);
        Task<bool> DeletePersonAsync(int id);
        Task<bool> AssignParentsAsync(AssignParentsDto assignParentsDto);
        Task<IEnumerable<PersonDto>> GetChildrenAsync(int parentId);
        Task<IEnumerable<PersonDto>> GetParentsAsync(int childId);
    }

    public class PersonService : IPersonService
    {
        private readonly IPersonRepository _personRepository;
        private readonly IPersonRelationRepository _relationRepository;
        private readonly IMapper _mapper;

        public PersonService(IPersonRepository personRepository, IPersonRelationRepository relationRepository, IMapper mapper)
        {
            _personRepository = personRepository;
            _relationRepository = relationRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<PersonDto>> GetAllPersonsAsync()
        {
            var persons = await _personRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<PersonDto>>(persons);
        }

        public async Task<PersonDto?> GetPersonByIdAsync(int id)
        {
            var person = await _personRepository.GetByIdAsync(id);
            return person != null ? _mapper.Map<PersonDto>(person) : null;
        }

        public async Task<IEnumerable<PersonDto>> SearchPersonsByNameAsync(string name)
        {
            var persons = await _personRepository.SearchByNameAsync(name);
            return _mapper.Map<IEnumerable<PersonDto>>(persons);
        }

        public async Task<PersonDto> CreatePersonAsync(CreatePersonDto createPersonDto)
        {
            var person = _mapper.Map<Person>(createPersonDto);
            var createdPerson = await _personRepository.AddAsync(person);
            return _mapper.Map<PersonDto>(createdPerson);
        }

        public async Task<PersonDto> UpdatePersonAsync(UpdatePersonDto updatePersonDto)
        {
            var person = _mapper.Map<Person>(updatePersonDto);
            var updatedPerson = await _personRepository.UpdateAsync(person);
            return _mapper.Map<PersonDto>(updatedPerson);
        }

        public async Task<bool> DeletePersonAsync(int id)
        {
            return await _personRepository.DeleteAsync(id);
        }

        public async Task<bool> AssignParentsAsync(AssignParentsDto assignParentsDto)
        {
            try
            {
                // Remove existing parent relations
                var existingRelations = await _relationRepository.GetRelationsByChildAsync(assignParentsDto.PersonId);
                foreach (var relation in existingRelations)
                {
                    await _relationRepository.DeleteAsync(relation.Id);
                }

                // Add father relation
                if (assignParentsDto.FatherId.HasValue)
                {
                    var fatherRelation = new PersonRelation
                    {
                        ParentId = assignParentsDto.FatherId.Value,
                        ChildId = assignParentsDto.PersonId,
                        RelationType = RelationType.Father
                    };
                    await _relationRepository.AddAsync(fatherRelation);
                }

                // Add mother relation
                if (assignParentsDto.MotherId.HasValue)
                {
                    var motherRelation = new PersonRelation
                    {
                        ParentId = assignParentsDto.MotherId.Value,
                        ChildId = assignParentsDto.PersonId,
                        RelationType = RelationType.Mother
                    };
                    await _relationRepository.AddAsync(motherRelation);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<IEnumerable<PersonDto>> GetChildrenAsync(int parentId)
        {
            var children = await _personRepository.GetChildrenAsync(parentId);
            return _mapper.Map<IEnumerable<PersonDto>>(children);
        }

        public async Task<IEnumerable<PersonDto>> GetParentsAsync(int childId)
        {
            var parents = await _personRepository.GetParentsAsync(childId);
            return _mapper.Map<IEnumerable<PersonDto>>(parents);
        }
    }
}
