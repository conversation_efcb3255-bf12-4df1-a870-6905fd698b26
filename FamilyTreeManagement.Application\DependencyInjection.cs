using Microsoft.Extensions.DependencyInjection;
using FamilyTreeManagement.Application.Services;
using FamilyTreeManagement.Application.Mappings;

namespace FamilyTreeManagement.Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            // Add AutoMapper
            services.AddAutoMapper(typeof(MappingProfile));

            // Add services
            services.AddScoped<IPersonService, PersonService>();
            services.AddScoped<IFamilyService, FamilyService>();

            return services;
        }
    }
}
