using AutoMapper;
using FamilyTreeManagement.Application.DTOs;
using FamilyTreeManagement.Domain.Entities;

namespace FamilyTreeManagement.Application.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Person mappings
            CreateMap<Person, PersonDto>()
                .ForMember(dest => dest.Age, opt => opt.MapFrom(src => 
                    src.DateOfBirth.HasValue ? 
                    (src.DateOfDeath ?? DateTime.Now).Year - src.DateOfBirth.Value.Year : 
                    (int?)null));

            CreateMap<CreatePersonDto, Person>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.RelationsAsParent, opt => opt.Ignore())
                .ForMember(dest => dest.RelationsAsChild, opt => opt.Ignore())
                .ForMember(dest => dest.FamiliesAsFather, opt => opt.Ignore())
                .ForMember(dest => dest.FamiliesAsMother, opt => opt.Ignore());

            CreateMap<UpdatePersonDto, Person>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.RelationsAsParent, opt => opt.Ignore())
                .ForMember(dest => dest.RelationsAsChild, opt => opt.Ignore())
                .ForMember(dest => dest.FamiliesAsFather, opt => opt.Ignore())
                .ForMember(dest => dest.FamiliesAsMother, opt => opt.Ignore());

            // Family mappings
            CreateMap<Family, FamilyDto>();
            CreateMap<CreateFamilyDto, Family>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.FatherId, opt => opt.Ignore())
                .ForMember(dest => dest.MotherId, opt => opt.Ignore())
                .ForMember(dest => dest.Father, opt => opt.Ignore())
                .ForMember(dest => dest.Mother, opt => opt.Ignore())
                .ForMember(dest => dest.Children, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());
        }
    }
}
