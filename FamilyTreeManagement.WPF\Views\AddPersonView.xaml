<UserControl x:Class="FamilyTreeManagement.WPF.Views.AddPersonView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Add Person Form with Material Design -->
    <materialDesign:Card xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes" Margin="16">
        <StackPanel Margin="24">
            <!-- Title -->
            <TextBlock Text="{Binding Path=DataContext.LocalizationService.AddNewPerson, RelativeSource={RelativeSource AncestorType=Window}}"
                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                     HorizontalAlignment="Center"
                     Margin="0,0,0,24"/>

            <!-- Person Information Section -->
            <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                  BorderThickness="1"
                  CornerRadius="4"
                  Padding="16"
                  Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="{Binding Path=DataContext.LocalizationService.PersonInformation, RelativeSource={RelativeSource AncestorType=Window}}"
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             Margin="0,0,0,16"/>

                    <!-- Name Fields Row -->
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding Path=DataContext.LocalizationService.FirstName, RelativeSource={RelativeSource AncestorType=Window}}"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                <TextBlock Text=" *" Foreground="Red" Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                            </StackPanel>
                            <TextBox Text="{Binding FirstName}"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   materialDesign:HintAssist.Hint="{Binding Path=DataContext.LocalizationService.FirstName, RelativeSource={RelativeSource AncestorType=Window}}"
                                   Margin="0,4,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding Path=DataContext.LocalizationService.LastName, RelativeSource={RelativeSource AncestorType=Window}}"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                <TextBlock Text=" *" Foreground="Red" Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                            </StackPanel>
                            <TextBox Text="{Binding LastName}"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   materialDesign:HintAssist.Hint="{Binding Path=DataContext.LocalizationService.LastName, RelativeSource={RelativeSource AncestorType=Window}}"
                                   Margin="0,4,0,0"/>
                        </StackPanel>
                    </Grid>

                    <!-- Details Row -->
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding Path=DataContext.LocalizationService.Gender, RelativeSource={RelativeSource AncestorType=Window}}"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                <TextBlock Text=" *" Foreground="Red" Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                            </StackPanel>
                            <ComboBox SelectedItem="{Binding SelectedGenderItem}"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    materialDesign:HintAssist.Hint="{Binding Path=DataContext.LocalizationService.SelectGender, RelativeSource={RelativeSource AncestorType=Window}}"
                                    Margin="0,4,0,0">
                                <ComboBoxItem Content="{Binding Path=DataContext.LocalizationService.Male, RelativeSource={RelativeSource AncestorType=Window}}" Tag="1"/>
                                <ComboBoxItem Content="{Binding Path=DataContext.LocalizationService.Female, RelativeSource={RelativeSource AncestorType=Window}}" Tag="2"/>
                                <ComboBoxItem Content="{Binding Path=DataContext.LocalizationService.Other, RelativeSource={RelativeSource AncestorType=Window}}" Tag="3"/>
                            </ComboBox>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="{Binding Path=DataContext.LocalizationService.DateOfBirth, RelativeSource={RelativeSource AncestorType=Window}}"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Margin="0,0,0,4"/>
                            <DatePicker SelectedDate="{Binding DateOfBirth}"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                      materialDesign:HintAssist.Hint="{Binding Path=DataContext.LocalizationService.DateOfBirth, RelativeSource={RelativeSource AncestorType=Window}}"/>
                        </StackPanel>

                        <StackPanel Grid.Column="4">
                            <TextBlock Text="{Binding Path=DataContext.LocalizationService.PlaceOfBirth, RelativeSource={RelativeSource AncestorType=Window}}"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Margin="0,0,0,4"/>
                            <TextBox Text="{Binding PlaceOfBirth}"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   materialDesign:HintAssist.Hint="{Binding Path=DataContext.LocalizationService.PlaceOfBirth, RelativeSource={RelativeSource AncestorType=Window}}"/>
                        </StackPanel>
                    </Grid>

                    <!-- Occupation -->
                    <StackPanel Margin="0,0,0,16">
                        <TextBlock Text="{Binding Path=DataContext.LocalizationService.Occupation, RelativeSource={RelativeSource AncestorType=Window}}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Margin="0,0,0,4"/>
                        <TextBox Text="{Binding Occupation}"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="{Binding Path=DataContext.LocalizationService.Occupation, RelativeSource={RelativeSource AncestorType=Window}}"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal"
                      HorizontalAlignment="Center"
                      Margin="0,16,0,0">
                <Button Content="{Binding Path=DataContext.LocalizationService.Clear, RelativeSource={RelativeSource AncestorType=Window}}"
                      Command="{Binding ClearFormCommand}"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="8,0"/>
                <Button Content="{Binding Path=DataContext.LocalizationService.AddPerson, RelativeSource={RelativeSource AncestorType=Window}}"
                      Command="{Binding AddPersonCommand}"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Margin="8,0"/>
                <Button Content="{Binding Path=DataContext.LocalizationService.Close, RelativeSource={RelativeSource AncestorType=Window}}"
                      Click="CloseButton_Click"
                      Style="{StaticResource MaterialDesignOutlinedSecondaryButton}"
                      Margin="8,0"/>
            </StackPanel>
        </StackPanel>
    </materialDesign:Card>
</UserControl>
