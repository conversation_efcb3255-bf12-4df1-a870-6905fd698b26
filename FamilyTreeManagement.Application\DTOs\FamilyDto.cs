namespace FamilyTreeManagement.Application.DTOs
{
    public class FamilyDto
    {
        public int Id { get; set; }
        public string FamilyName { get; set; } = string.Empty;
        public PersonDto? Father { get; set; }
        public PersonDto? Mother { get; set; }
        public DateTime? MarriageDate { get; set; }
        public string? MarriagePlace { get; set; }
        public string? Notes { get; set; }
        public List<PersonDto> Children { get; set; } = new();
    }

    public class CreateFamilyDto
    {
        public string FamilyName { get; set; } = string.Empty;
        public CreatePersonDto? Father { get; set; }
        public CreatePersonDto? Mother { get; set; }
        public DateTime? MarriageDate { get; set; }
        public string? MarriagePlace { get; set; }
        public string? Notes { get; set; }
        public List<CreatePersonDto> Children { get; set; } = new();
    }

    public class AssignParentsDto
    {
        public int PersonId { get; set; }
        public int? FatherId { get; set; }
        public int? MotherId { get; set; }
    }
}
