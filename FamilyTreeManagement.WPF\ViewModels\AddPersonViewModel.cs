using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FamilyTreeManagement.Application.Services;
using FamilyTreeManagement.Application.DTOs;
using FamilyTreeManagement.Domain.Enums;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;
using FamilyTreeManagement.WPF.Helpers.Services;

namespace FamilyTreeManagement.WPF.ViewModels
{
    public partial class AddPersonViewModel : ObservableObject
    {
        private readonly IPersonService _personService;
        private readonly ILogger<AddPersonViewModel> _logger;

        public ILogger Logger => _logger;

        [ObservableProperty]
        private LocalizationService _localizationService;
        
        [ObservableProperty]
        private string firstName = string.Empty;

        [ObservableProperty]
        private string lastName = string.Empty;

        [ObservableProperty]
        private DateTime? dateOfBirth;

        [ObservableProperty]
        private DateTime? dateOfDeath;

        [ObservableProperty]
        private Gender gender = Gender.Male;

        [ObservableProperty]
        private ComboBoxItem? selectedGenderItem;

        [ObservableProperty]
        private string placeOfBirth = string.Empty;

        [ObservableProperty]
        private string occupation = string.Empty;

        [ObservableProperty]
        private string notes = string.Empty;

        public event Action? PersonAdded;

        public string this[string key] => LocalizationService[key];
        public string this[string key, params object[] arguments] => LocalizationService[key, arguments];

        public AddPersonViewModel(IPersonService personService
            , ILogger<AddPersonViewModel> logger
            , LocalizationService localizationService)
        {
            _personService = personService;
            _logger = logger;
            _localizationService = localizationService;
            // Set default gender selection

            SelectedGenderItem = new ComboBoxItem { Content = "Male", Tag = 1 };
        }

        [RelayCommand]
        private async Task AddPersonAsync()
        {
            _logger.LogInformation("🚀 AddPersonAsync started");
            _logger.LogInformation("📝 Form data: FirstName='{FirstName}', LastName='{LastName}', Gender='{Gender}', DateOfBirth='{DateOfBirth}', PlaceOfBirth='{PlaceOfBirth}', Occupation='{Occupation}'",
                FirstName, LastName, SelectedGenderItem?.Content, DateOfBirth, PlaceOfBirth, Occupation);

            try
            {
                // Validation
                _logger.LogInformation("🔍 Starting validation...");
                if (string.IsNullOrWhiteSpace(FirstName) || string.IsNullOrWhiteSpace(LastName))
                {
                    _logger.LogWarning("❌ Validation failed: Missing FirstName or LastName");
                    System.Windows.MessageBox.Show("❌ Please enter both First Name and Last Name.", "Missing Required Fields",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    return;
                }

                if (SelectedGenderItem == null)
                {
                    _logger.LogWarning("❌ Validation failed: Missing Gender selection");
                    System.Windows.MessageBox.Show("❌ Please select a Gender.", "Missing Required Field",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    return;
                }

                _logger.LogInformation("✅ Validation passed");

                // Get gender from selected item
                _logger.LogInformation("🔄 Processing gender selection...");
                var selectedGender = Gender.Male; // default
                if (SelectedGenderItem?.Tag != null)
                {
                    if (int.TryParse(SelectedGenderItem.Tag.ToString(), out int genderValue))
                    {
                        selectedGender = (Gender)genderValue;
                        _logger.LogInformation("✅ Gender parsed: {Gender} (value: {GenderValue})", selectedGender, genderValue);
                    }
                }

                var createPersonDto = new CreatePersonDto
                {
                    FirstName = FirstName.Trim(),
                    LastName = LastName.Trim(),
                    DateOfBirth = DateOfBirth,
                    DateOfDeath = DateOfDeath,
                    Gender = selectedGender,
                    PlaceOfBirth = PlaceOfBirth?.Trim(),
                    Occupation = Occupation?.Trim(),
                    Notes = Notes?.Trim()
                };

                _logger.LogInformation("📦 CreatePersonDto created: {@CreatePersonDto}", createPersonDto);
                _logger.LogInformation("🔄 Calling PersonService.CreatePersonAsync...");

                await _personService.CreatePersonAsync(createPersonDto);

                _logger.LogInformation("✅ Person created successfully in database");

                // Show detailed success message
                var successMessage = $"✅ SUCCESS!\n\n" +
                                   $"Person Added Successfully:\n" +
                                   $"• Name: {FirstName} {LastName}\n" +
                                   $"• Gender: {SelectedGenderItem.Content}\n" +
                                   $"• Date of Birth: {(DateOfBirth?.ToString("yyyy-MM-dd") ?? "Not specified")}\n" +
                                   $"• Place of Birth: {(string.IsNullOrEmpty(PlaceOfBirth) ? "Not specified" : PlaceOfBirth)}\n" +
                                   $"• Occupation: {(string.IsNullOrEmpty(Occupation) ? "Not specified" : Occupation)}\n\n" +
                                   $"The person will now appear in the person list on the left.";

                _logger.LogInformation("🎉 Showing success message to user");
                System.Windows.MessageBox.Show(successMessage, "Person Added Successfully!",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                // Clear the form for next person
                _logger.LogInformation("🧹 Clearing form for next person");
                ClearFormSilently();

                // Invoke PersonAdded event to refresh the person list
                _logger.LogInformation("🔄 Invoking PersonAdded event to refresh UI");
                PersonAdded?.Invoke();

                _logger.LogInformation("✅ AddPersonAsync completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ ERROR in AddPersonAsync: Failed to add person {FirstName} {LastName}", FirstName, LastName);

                var errorMessage = $"❌ ERROR ADDING PERSON\n\n" +
                                 $"Failed to add: {FirstName} {LastName}\n\n" +
                                 $"Error Details: {ex.Message}\n\n" +
                                 $"Please check:\n" +
                                 $"• Database connection\n" +
                                 $"• All required fields are filled\n" +
                                 $"• Try again or contact support";

                _logger.LogInformation("🚨 Showing error message to user");
                System.Windows.MessageBox.Show(errorMessage, "Error Adding Person",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ClearForm()
        {
            ClearFormSilently();

            // Show confirmation
            System.Windows.MessageBox.Show("🧹 Form cleared!\n\nAll fields have been reset to default values.",
                "Form Cleared", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }

        private void ClearFormSilently()
        {
            FirstName = string.Empty;
            LastName = string.Empty;
            DateOfBirth = null;
            DateOfDeath = null;
            Gender = Gender.Male;
            SelectedGenderItem = new ComboBoxItem { Content = "Male", Tag = 1 };
            PlaceOfBirth = string.Empty;
            Occupation = string.Empty;
            Notes = string.Empty;
        }
    }
}
