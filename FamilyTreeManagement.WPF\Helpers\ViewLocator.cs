using System;
using System.Windows;
using System.Windows.Controls;

namespace FamilyTreeManagement.WPF.Converters.Helpers
{
    public class ViewLocator : DataTemplateSelector
    {
        public override DataTemplate SelectTemplate(object item, DependencyObject container)
        {
            if (item == null) return null;

            var viewModelType = item.GetType();
            var viewTypeName = viewModelType.FullName.Replace("ViewModel", "View");
            var viewAssembly = viewModelType.Assembly;

            var viewType = viewAssembly.GetType(viewTypeName);
            if (viewType == null)
                throw new InvalidOperationException($"View not found for {viewModelType.Name}");

            var factory = new FrameworkElementFactory(viewType);
            var template = new DataTemplate { VisualTree = factory };

            return template;
        }
    }
}
