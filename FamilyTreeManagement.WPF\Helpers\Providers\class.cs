


using System.IO;
using Newtonsoft.Json;

namespace FamilyTreeManagement.WPF.Helpers.Providers;


public class JsonLocalizationProvider
{
    private readonly string _resourcesPath;
    private Dictionary<string, Dictionary<string, string>> _localizations = new();

    public JsonLocalizationProvider(string resourcesPath = "./Resources/Locales")
    {
        _resourcesPath = resourcesPath;
        LoadAllCultures();
    }

    private void LoadAllCultures()
    {
        foreach (var file in Directory.GetFiles(_resourcesPath, "*.json"))
        {
            var cultureName = Path.GetFileNameWithoutExtension(file);
            var json = File.ReadAllText(file);
            _localizations[cultureName] = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
        }
    }

    public string? GetString(string key, string culture)
    {
        if (_localizations.TryGetValue(culture, out var cultureDict))
        {
            return cultureDict.TryGetValue(key, out var value) ? value : null;
        }
        return null;
    }

    public string? GetString(string key, string culture, params object[] arguments)
    {
        if (_localizations.TryGetValue(culture, out var cultureDict))
        {
            return cultureDict.TryGetValue(key, out var value) ? string.Format(value, arguments) : null;
        }
        return null;
    }
}