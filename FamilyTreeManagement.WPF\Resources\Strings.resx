<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Main Window -->
  <data name="AppTitle" xml:space="preserve">
    <value>Family Tree Management</value>
  </data>
  <data name="AddPerson" xml:space="preserve">
    <value>Add Person</value>
  </data>
  <data name="RegisterFamily" xml:space="preserve">
    <value>Register Family</value>
  </data>
  <data name="AssignParents" xml:space="preserve">
    <value>Assign Parents</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="SearchPersons" xml:space="preserve">
    <value>Search persons...</value>
  </data>
  <data name="TotalPersons" xml:space="preserve">
    <value>Total Persons: {0}</value>
  </data>
  <data name="Families" xml:space="preserve">
    <value>Families</value>
  </data>

  <!-- Add Person Form -->
  <data name="AddNewPerson" xml:space="preserve">
    <value>Add New Person</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="DateOfBirth" xml:space="preserve">
    <value>Date of Birth</value>
  </data>
  <data name="PlaceOfBirth" xml:space="preserve">
    <value>Place of Birth</value>
  </data>
  <data name="Occupation" xml:space="preserve">
    <value>Occupation</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>

  <!-- Messages -->
  <data name="Success" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="PersonAddedSuccessfully" xml:space="preserve">
    <value>Person Added Successfully!</value>
  </data>
  <data name="FormCleared" xml:space="preserve">
    <value>Form cleared!</value>
  </data>
  <data name="MissingRequiredFields" xml:space="preserve">
    <value>Please enter both First Name and Last Name.</value>
  </data>
  <data name="MissingGender" xml:space="preserve">
    <value>Please select a Gender.</value>
  </data>

  <!-- Register Family -->
  <data name="RegisterNewFamily" xml:space="preserve">
    <value>Register New Family</value>
  </data>
  <data name="FamilyName" xml:space="preserve">
    <value>Family Name</value>
  </data>
  <data name="Father" xml:space="preserve">
    <value>Father</value>
  </data>
  <data name="Mother" xml:space="preserve">
    <value>Mother</value>
  </data>
  <data name="MarriageDate" xml:space="preserve">
    <value>Marriage Date</value>
  </data>
  <data name="RegisterFamily" xml:space="preserve">
    <value>Register Family</value>
  </data>

  <!-- Assign Parents -->
  <data name="AssignParentsTitle" xml:space="preserve">
    <value>Assign Parents</value>
  </data>
  <data name="SelectPerson" xml:space="preserve">
    <value>Select Person</value>
  </data>
  <data name="SearchAvailablePersons" xml:space="preserve">
    <value>Search available persons...</value>
  </data>
  <data name="AssignParentsButton" xml:space="preserve">
    <value>Assign Parents</value>
  </data>

  <!-- Common -->
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Age" xml:space="preserve">
    <value>Age: {0}</value>
  </data>
  <data name="PersonInformation" xml:space="preserve">
    <value>Person Information</value>
  </data>
  <data name="SelectGender" xml:space="preserve">
    <value>Select Gender</value>
  </data>
</root>
