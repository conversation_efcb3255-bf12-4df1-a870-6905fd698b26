using Microsoft.EntityFrameworkCore;
using FamilyTreeManagement.Domain.Entities;
using FamilyTreeManagement.Domain.Interfaces;
using FamilyTreeManagement.Infrastructure.Data;

namespace FamilyTreeManagement.Infrastructure.Repositories
{
    public class PersonRepository : IPersonRepository
    {
        private readonly FamilyTreeContext _context;

        public PersonRepository(FamilyTreeContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Person>> GetAllAsync()
        {
            return await _context.Persons
                .OrderBy(p => p.LastName)
                .ThenBy(p => p.FirstName)
                .ToListAsync();
        }

        public async Task<Person?> GetByIdAsync(int id)
        {
            return await _context.Persons.FindAsync(id);
        }

        public async Task<Person?> GetByIdWithRelationsAsync(int id)
        {
            return await _context.Persons
                .Include(p => p.RelationsAsParent)
                    .ThenInclude(r => r.Child)
                .Include(p => p.RelationsAsChild)
                    .ThenInclude(r => r.Parent)
                .Include(p => p.Families<PERSON>)
                .Include(p => p.Families<PERSON>Mother)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<IEnumerable<Person>> SearchByNameAsync(string name)
        {
            return await _context.Persons
                .Where(p => p.FirstName.Contains(name) || p.LastName.Contains(name))
                .OrderBy(p => p.LastName)
                .ThenBy(p => p.FirstName)
                .ToListAsync();
        }

        public async Task<Person> AddAsync(Person person)
        {
            _context.Persons.Add(person);
            await _context.SaveChangesAsync();
            return person;
        }

        public async Task<Person> UpdateAsync(Person person)
        {
            person.UpdatedAt = DateTime.UtcNow;
            _context.Entry(person).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return person;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var person = await _context.Persons.FindAsync(id);
            if (person == null) return false;

            _context.Persons.Remove(person);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Person>> GetChildrenAsync(int parentId)
        {
            return await _context.PersonRelations
                .Where(r => r.ParentId == parentId)
                .Select(r => r.Child)
                .ToListAsync();
        }

        public async Task<IEnumerable<Person>> GetParentsAsync(int childId)
        {
            return await _context.PersonRelations
                .Where(r => r.ChildId == childId)
                .Select(r => r.Parent)
                .ToListAsync();
        }

        public async Task<IEnumerable<Person>> GetSiblingsAsync(int personId)
        {
            var parentIds = await _context.PersonRelations
                .Where(r => r.ChildId == personId)
                .Select(r => r.ParentId)
                .ToListAsync();

            return await _context.PersonRelations
                .Where(r => parentIds.Contains(r.ParentId) && r.ChildId != personId)
                .Select(r => r.Child)
                .Distinct()
                .ToListAsync();
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Persons.AnyAsync(p => p.Id == id);
        }
    }
}
