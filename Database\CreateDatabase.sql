-- Family Tree Management Database Creation Script
-- MySQL Database

-- Create database
CREATE DATABASE IF NOT EXISTS FamilyTreeDB;
USE FamilyTreeDB;

-- Create Persons table
CREATE TABLE IF NOT EXISTS Persons (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    FirstName VARCHAR(100) NOT NULL,
    LastName VARCHAR(100) NOT NULL,
    DateOfBirth DATE NULL,
    DateOfDeath DATE NULL,
    Gender INT NOT NULL,
    PlaceOfBirth VARCHAR(200) NULL,
    Occupation VARCHAR(100) NULL,
    Notes TEXT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME NULL,
    INDEX idx_name (FirstName, LastName)
);

-- Create PersonRelations table (for recursive family relationships)
CREATE TABLE IF NOT EXISTS PersonRelations (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    ParentId INT NOT NULL,
    ChildId INT NOT NULL,
    RelationType INT NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    Notes VARCHAR(500) NULL,
    FOREIGN KEY (ParentId) REFERENCES Persons(Id) ON DELETE RESTRICT,
    FOREIGN KEY (ChildId) REFERENCES Persons(Id) ON DELETE RESTRICT,
    UNIQUE KEY unique_relation (ParentId, ChildId, RelationType)
);

-- Create Families table
CREATE TABLE IF NOT EXISTS Families (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    FamilyName VARCHAR(200) NOT NULL,
    FatherId INT NULL,
    MotherId INT NULL,
    MarriageDate DATE NULL,
    MarriagePlace VARCHAR(200) NULL,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME NULL,
    Notes TEXT NULL,
    FOREIGN KEY (FatherId) REFERENCES Persons(Id) ON DELETE SET NULL,
    FOREIGN KEY (MotherId) REFERENCES Persons(Id) ON DELETE SET NULL
);

-- Insert sample data
INSERT INTO Persons (FirstName, LastName, DateOfBirth, Gender, PlaceOfBirth) VALUES
('John', 'Smith', '1950-01-15', 1, 'New York'),
('Mary', 'Johnson', '1952-03-20', 2, 'California'),
('Michael', 'Smith', '1975-07-10', 1, 'Texas'),
('Sarah', 'Smith', '1978-11-05', 2, 'Texas'),
('David', 'Smith', '1980-09-12', 1, 'Texas');

-- Create family relationships
INSERT INTO Families (FamilyName, FatherId, MotherId, MarriageDate, MarriagePlace) VALUES
('Smith Family', 1, 2, '1974-06-15', 'Las Vegas');

-- Create parent-child relationships
INSERT INTO PersonRelations (ParentId, ChildId, RelationType) VALUES
(1, 3, 1), -- John is father of Michael
(2, 3, 2), -- Mary is mother of Michael
(1, 4, 1), -- John is father of Sarah
(2, 4, 2), -- Mary is mother of Sarah
(1, 5, 1), -- John is father of David
(2, 5, 2); -- Mary is mother of David

-- Relation Types:
-- 1 = Father
-- 2 = Mother
-- 3 = Son
-- 4 = Daughter
-- 5 = Spouse

-- Gender Types:
-- 1 = Male
-- 2 = Female
-- 3 = Other
