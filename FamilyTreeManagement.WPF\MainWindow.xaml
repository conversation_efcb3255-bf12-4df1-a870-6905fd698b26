<Window x:Class="FamilyTreeManagement.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:views="clr-namespace:FamilyTreeManagement.WPF.Views"
        Title="{Binding LocalizationService.AppTitle}"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="{Binding LocalizationService.FlowDirection}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <DockPanel>
                    <materialDesign:PackIcon Kind="FamilyTree"
                                           Width="32" Height="32"
                                           VerticalAlignment="Center"
                                           DockPanel.Dock="Left"/>
                    <TextBlock Text="{Binding LocalizationService.AppTitle}"
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"
                             Margin="16,0,0,0"
                             DockPanel.Dock="Left"/>

                    <StackPanel Orientation="Horizontal"
                              DockPanel.Dock="Right"
                              HorizontalAlignment="Right">
                        <!-- Language Switcher -->
                        <StackPanel Orientation="Horizontal" Margin="8,0">
                            <Button Content="🇸🇦 العربية"
                                  Command="{Binding SwitchToArabicCommand}"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="4,0" Padding="8,4"/>
                            <Button Content="🇺🇸 English"
                                  Command="{Binding SwitchToEnglishCommand}"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="4,0" Padding="8,4"/>
                        </StackPanel>

                        <!-- Main Action Buttons -->
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                              Content="{Binding LocalizationService.AddPerson}"
                              Command="{Binding ShowAddPersonCommand}"
                              Margin="8,0"/>
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                              Content="{Binding LocalizationService.RegisterFamily}"
                              Command="{Binding ShowRegisterFamilyCommand}"
                              Margin="8,0"/>
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                              Content="{Binding LocalizationService.AssignParents}"
                              Command="{Binding ShowAssignParentsCommand}"
                              Margin="8,0"/>
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                              Content="{Binding LocalizationService.Refresh}"
                              Command="{Binding LoadDataCommand}"
                              Margin="8,0"/>
                    </StackPanel>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="400"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Panel - Person List -->
                <materialDesign:Card Grid.Column="0" Margin="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Search -->
                        <TextBox Grid.Row="0"
                               Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                               materialDesign:HintAssist.Hint="{Binding LocalizationService.SearchPersons}"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               Margin="16">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding SearchPersonsCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>

                        <!-- Person Count -->
                        <TextBlock Grid.Row="1"
                                 Text="{Binding Persons.Count, StringFormat='Total Persons: {0}'}"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Margin="16,0,16,8"/>

                        <!-- Person List -->
                        <ListBox Grid.Row="2"
                               ItemsSource="{Binding Persons}"
                               SelectedItem="{Binding SelectedPerson}"
                               Style="{StaticResource MaterialDesignListBox}"
                               Margin="16,0,16,16">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Column="0"
                                                               Kind="Account"
                                                               Width="24" Height="24"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,12,0"/>

                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding FullName}"
                                                     Style="{StaticResource MaterialDesignSubtitle2TextBlock}"/>
                                            <TextBlock Text="{Binding Age, StringFormat='Age: {0}'}"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     Opacity="0.68"/>
                                        </StackPanel>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </Grid>
                </materialDesign:Card>

                <!-- Right Panel - Dynamic Content -->
                <ContentControl Grid.Column="1" Margin="0,16,16,16">
                    <ContentControl.Style>
                        <Style TargetType="ContentControl">

                            <!-- Default: Show Family List -->
                            <Setter Property="Content">
                                <Setter.Value>
                                    <materialDesign:Card>
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="*"/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0"
                                                     Text="{Binding LocalizationService.Families}"
                                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                     Margin="16"/>

                                            <ListBox Grid.Row="1"
                                                   ItemsSource="{Binding Families}"
                                                   Style="{StaticResource MaterialDesignListBox}"
                                                   Margin="16">
                                                <ListBox.ItemTemplate>
                                                    <DataTemplate>
                                                        <Grid Margin="0,8">
                                                            <StackPanel>
                                                                <TextBlock Text="{Binding FamilyName}"
                                                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>
                                                                <TextBlock Text="{Binding Father.FullName, StringFormat='Father: {0}'}"
                                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                                         Opacity="0.68"/>
                                                                <TextBlock Text="{Binding Mother.FullName, StringFormat='Mother: {0}'}"
                                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                                         Opacity="0.68"/>
                                                            </StackPanel>
                                                        </Grid>
                                                    </DataTemplate>
                                                </ListBox.ItemTemplate>
                                            </ListBox>
                                        </Grid>
                                    </materialDesign:Card>
                                </Setter.Value>
                            </Setter>

                            <Style.Triggers>
                                <!-- Show Add Person View -->
                                <DataTrigger Binding="{Binding IsAddPersonVisible}" Value="True">
                                    <Setter Property="Content">
                                        <Setter.Value>
                                            <views:AddPersonView DataContext="{Binding AddPersonViewModel}"/>
                                        </Setter.Value>
                                    </Setter>
                                </DataTrigger>

                                <!-- Show Register Family View -->
                                <DataTrigger Binding="{Binding IsRegisterFamilyVisible}" Value="True">
                                    <Setter Property="Content">
                                        <Setter.Value>
                                            <views:RegisterFamilyView DataContext="{Binding RegisterFamilyViewModel}" />
                                        </Setter.Value>
                                    </Setter>
                                </DataTrigger>

                                <!-- Show Assign Parents View -->
                                <DataTrigger Binding="{Binding IsAssignParentsVisible}" Value="True">
                                    <Setter Property="Content">
                                        <Setter.Value>
                                            <views:AssignParentsView DataContext="{Binding AssignParentsViewModel}"/>
                                        </Setter.Value>
                                    </Setter>
                                </DataTrigger>
                            </Style.Triggers>

                        </Style>
                    </ContentControl.Style>
                </ContentControl>
            </Grid>
        </Grid>
    </materialDesign:DialogHost>
</Window>
