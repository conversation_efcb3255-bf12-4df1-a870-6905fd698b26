using System.Windows.Controls;
using System.Windows;
using FamilyTreeManagement.WPF.ViewModels;

namespace FamilyTreeManagement.WPF.Views
{
    public partial class RegisterFamilyView : UserControl
    {
        public RegisterFamilyView()
        {
            InitializeComponent();
            System.Diagnostics.Debug.WriteLine("RegisterFamilyView created");
            System.Diagnostics.Debug.WriteLine("DataContext: " + DataContext);
        }

        private void AddChildButton_Click(object sender, RoutedEventArgs e)
        {
            // Debug logging
            System.Diagnostics.Debug.WriteLine("🔥 AddChildButton_Click event fired!");

            // Check what DataContext we actually have
            var dataContextType = DataContext?.GetType().Name ?? "null";
            var dataContextString = DataContext?.ToString() ?? "null";

            MessageBox.Show($"🔍 DETAILED DEBUG INFO:\n\n" +
                          $"✅ Button Click Event Fired!\n" +
                          $"📋 DataContext Type: {dataContextType}\n" +
                          $"📋 DataContext Value: {dataContextString}\n" +
                          $"📋 DataContext is RegisterFamilyViewModel: {DataContext is RegisterFamilyViewModel}\n" +
                          $"📋 DataContext is null: {DataContext == null}",
                          "Debug Info", MessageBoxButton.OK, MessageBoxImage.Information);

            // Try to execute the command manually
            if (DataContext is RegisterFamilyViewModel viewModel)
            {
                System.Diagnostics.Debug.WriteLine("✅ ViewModel found, executing AddChildCommand");
                MessageBox.Show("✅ ViewModel found! Executing command...", "Debug", MessageBoxButton.OK, MessageBoxImage.Information);

                if (viewModel.AddChildCommand.CanExecute(null))
                {
                    System.Diagnostics.Debug.WriteLine("✅ Command can execute, executing now...");
                    viewModel.AddChildCommand.Execute(null);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ Command cannot execute");
                    MessageBox.Show("❌ Command cannot execute!", "Debug", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("❌ ViewModel not found in DataContext");
                MessageBox.Show($"❌ ViewModel not found in DataContext!\n\nDataContext is: {dataContextType}", "Debug", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
