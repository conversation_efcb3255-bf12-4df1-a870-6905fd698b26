using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FamilyTreeManagement.Application.Services;
using FamilyTreeManagement.Application.DTOs;
using FamilyTreeManagement.Domain.Enums;
using System.Collections.ObjectModel;
using Microsoft.Extensions.Logging;
using System.Windows.Controls;

namespace FamilyTreeManagement.WPF.ViewModels
{
    public partial class RegisterFamilyViewModel : ObservableObject
    {
        private readonly IPersonService _personService;
        private readonly IFamilyService _familyService;
        private readonly ILogger<RegisterFamilyViewModel> _logger;

        public DateTime  DateTime { get; set; }
        public string DateTimeString => DateTime.ToString();

        [ObservableProperty]
        private string familyName = string.Empty;

        [ObservableProperty]
        private string fatherFirstName = string.Empty;

        [ObservableProperty]
        private string fatherLastName = string.Empty;

        [ObservableProperty]
        private DateTime? fatherDateOfBirth;

        [ObservableProperty]
        private string motherFirstName = string.Empty;

        [ObservableProperty]
        private string motherLastName = string.Empty;

        [ObservableProperty]
        private DateTime? motherDateOfBirth;

        [ObservableProperty]
        private DateTime? marriageDate;

        [ObservableProperty]
        private string marriagePlace = string.Empty;

        [ObservableProperty]
        private ObservableCollection<CreatePersonDto> children = new();

        [ObservableProperty]
        private string childFirstName = string.Empty;

        [ObservableProperty]
        private string childLastName = string.Empty;

        [ObservableProperty]
        private DateTime? childDateOfBirth;

        [ObservableProperty]
        private Gender childGender = Gender.Male;

        [ObservableProperty]
        private ComboBoxItem? selectedChildGenderItem;

        public event Action? FamilyRegistered;

        public RegisterFamilyViewModel(IPersonService personService, IFamilyService familyService
            , ILogger<RegisterFamilyViewModel> logger
        )
        {
            _personService = personService;
            _familyService = familyService;
            _logger = logger;
            DateTime = DateTime.Now;
        }

        [RelayCommand]
        private void AddChild()
        {
            _logger.LogInformation("🚀 AddChild button clicked!");
            // _logger.LogInformation("📝 Child data: FirstName='{FirstName}', LastName='{LastName}', DateOfBirth='{DateOfBirth}', SelectedGenderItem='{GenderItem}'",
            //   ChildFirstName, ChildLastName, ChildDateOfBirth, SelectedChildGenderItem?.Content);

            if (string.IsNullOrWhiteSpace(ChildFirstName) || string.IsNullOrWhiteSpace(ChildLastName))
            {
                // _logger.LogWarning("❌ Validation failed: Missing child name");
                System.Windows.MessageBox.Show("Please enter child's first and last name.", "Validation Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }

            // Get gender from selected item
            var selectedGender = Gender.Male; // default
            if (SelectedChildGenderItem?.Tag != null)
            {
                if (int.TryParse(SelectedChildGenderItem.Tag.ToString(), out int genderValue))
                {
                    selectedGender = (Gender)genderValue;
                    _logger.LogInformation("✅ Gender parsed: {Gender} (value: {GenderValue})", selectedGender, genderValue);
                }
            }

            var child = new CreatePersonDto
            {
                FirstName = ChildFirstName,
                LastName = ChildLastName,
                DateOfBirth = ChildDateOfBirth,
                Gender = selectedGender
            };

            _logger.LogInformation("📦 Adding child to collection: {@Child}", child);
            Children.Add(child);
            _logger.LogInformation("✅ Child added successfully. Total children: {Count}", Children.Count);

            // Clear child form
            ChildFirstName = string.Empty;
            ChildLastName = string.Empty;
            ChildDateOfBirth = null;
            SelectedChildGenderItem = null;
            _logger.LogInformation("🧹 Child form cleared");
        }

        [RelayCommand]
        private void RemoveChild(CreatePersonDto child)
        {
            Children.Remove(child);
        }

        [RelayCommand]
        private async Task RegisterFamilyAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(FamilyName))
                {
                    System.Windows.MessageBox.Show("Please enter a family name.", "Validation Error",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    return;
                }

                var createFamilyDto = new CreateFamilyDto
                {
                    FamilyName = FamilyName,
                    MarriageDate = MarriageDate,
                    MarriagePlace = MarriagePlace,
                    Children = Children.ToList()
                };

                // Add father if provided
                if (!string.IsNullOrWhiteSpace(FatherFirstName) && !string.IsNullOrWhiteSpace(FatherLastName))
                {
                    createFamilyDto.Father = new CreatePersonDto
                    {
                        FirstName = FatherFirstName,
                        LastName = FatherLastName,
                        DateOfBirth = FatherDateOfBirth,
                        Gender = Gender.Male
                    };
                }

                // Add mother if provided
                if (!string.IsNullOrWhiteSpace(MotherFirstName) && !string.IsNullOrWhiteSpace(MotherLastName))
                {
                    createFamilyDto.Mother = new CreatePersonDto
                    {
                        FirstName = MotherFirstName,
                        LastName = MotherLastName,
                        DateOfBirth = MotherDateOfBirth,
                        Gender = Gender.Female
                    };
                }

                await _familyService.CreateFamilyAsync(createFamilyDto);

                System.Windows.MessageBox.Show("Family registered successfully!", "Success",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                ClearForm();
                FamilyRegistered?.Invoke();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error registering family: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ClearForm()
        {
            FamilyName = string.Empty;
            FatherFirstName = string.Empty;
            FatherLastName = string.Empty;
            FatherDateOfBirth = null;
            MotherFirstName = string.Empty;
            MotherLastName = string.Empty;
            MotherDateOfBirth = null;
            MarriageDate = null;
            MarriagePlace = string.Empty;
            Children.Clear();
            ChildFirstName = string.Empty;
            ChildLastName = string.Empty;
            ChildDateOfBirth = null;
            ChildGender = Gender.Male;
        }
    }
}
