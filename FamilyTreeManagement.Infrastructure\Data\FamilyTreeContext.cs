using Microsoft.EntityFrameworkCore;
using FamilyTreeManagement.Domain.Entities;
using FamilyTreeManagement.Domain.Enums;

namespace FamilyTreeManagement.Infrastructure.Data
{
    public class FamilyTreeContext : DbContext
    {
        public FamilyTreeContext(DbContextOptions<FamilyTreeContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);
        }


        public DbSet<Person> Persons { get; set; }
        public DbSet<PersonRelation> PersonRelations { get; set; }
        public DbSet<Family> Families { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Person entity configuration
            modelBuilder.Entity<Person>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PlaceOfBirth).HasMaxLength(200);
                entity.Property(e => e.Occupation).HasMaxLength(100);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.Gender).HasConversion<int>();
                
                entity.HasIndex(e => new { e.FirstName, e.LastName });
            });

            // PersonRelation entity configuration
            modelBuilder.Entity<PersonRelation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.RelationType).HasConversion<int>();
                entity.Property(e => e.Notes).HasMaxLength(500);

                // Configure relationships
                entity.HasOne(e => e.Parent)
                    .WithMany(p => p.RelationsAsParent)
                    .HasForeignKey(e => e.ParentId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Child)
                    .WithMany(p => p.RelationsAsChild)
                    .HasForeignKey(e => e.ChildId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Unique constraint to prevent duplicate relations
                entity.HasIndex(e => new { e.ParentId, e.ChildId, e.RelationType }).IsUnique();
            });

            // Family entity configuration
            modelBuilder.Entity<Family>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FamilyName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.MarriagePlace).HasMaxLength(200);
                entity.Property(e => e.Notes).HasMaxLength(1000);

                // Configure relationships
                entity.HasOne(e => e.Father)
                    .WithMany(p => p.FamiliesAsFather)
                    .HasForeignKey(e => e.FatherId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Mother)
                    .WithMany(p => p.FamiliesAsMother)
                    .HasForeignKey(e => e.MotherId)
                    .OnDelete(DeleteBehavior.SetNull);
            });
        }
    }
}
