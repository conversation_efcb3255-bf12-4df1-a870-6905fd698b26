using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FamilyTreeManagement.Application.Services;
using FamilyTreeManagement.Application.DTOs;
using System.Collections.ObjectModel;

namespace FamilyTreeManagement.WPF.ViewModels
{
    public partial class AssignParentsViewModel : ObservableObject
    {
        private readonly IPersonService _personService;

        [ObservableProperty]
        private PersonDto selectedPerson;

        [ObservableProperty]
        private ObservableCollection<PersonDto> availablePersons = new();

        [ObservableProperty]
        private PersonDto? selectedFather;

        [ObservableProperty]
        private PersonDto? selectedMother;

        [ObservableProperty]
        private string searchText = string.Empty;

        public event Action? ParentsAssigned;

        public AssignParentsViewModel(IPersonService personService, PersonDto selectedPerson)
        {
            _personService = personService;
            SelectedPerson = selectedPerson;
            _ = LoadAvailablePersonsAsync();
        }

        [RelayCommand]
        private async Task LoadAvailablePersonsAsync()
        {
            try
            {
                var persons = await _personService.GetAllPersonsAsync();
                AvailablePersons.Clear();

                foreach (var person in persons.Where(p => p.Id != SelectedPerson.Id))
                {
                    AvailablePersons.Add(person);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error loading persons: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task SearchPersonsAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadAvailablePersonsAsync();
                return;
            }

            try
            {
                var searchResults = await _personService.SearchPersonsByNameAsync(SearchText);
                AvailablePersons.Clear();

                foreach (var person in searchResults.Where(p => p.Id != SelectedPerson.Id))
                {
                    AvailablePersons.Add(person);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error searching: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task AssignParentsAsync()
        {
            try
            {
                var assignParentsDto = new AssignParentsDto
                {
                    PersonId = SelectedPerson.Id,
                    FatherId = SelectedFather?.Id,
                    MotherId = SelectedMother?.Id
                };

                var success = await _personService.AssignParentsAsync(assignParentsDto);

                if (success)
                {
                    System.Windows.MessageBox.Show("Parents assigned successfully!", "Success",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                    ParentsAssigned?.Invoke();
                }
                else
                {
                    System.Windows.MessageBox.Show("Failed to assign parents.", "Error",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error assigning parents: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void SetFather(PersonDto person)
        {
            SelectedFather = person;
        }

        [RelayCommand]
        private void SetMother(PersonDto person)
        {
            SelectedMother = person;
        }

        [RelayCommand]
        private void ClearSelection()
        {
            SelectedFather = null;
            SelectedMother = null;
        }
    }
}
