using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.IO;
using System.Windows;
using System.Globalization;
using FamilyTreeManagement.Application;
using FamilyTreeManagement.Infrastructure;
using FamilyTreeManagement.WPF.ViewModels;
using FamilyTreeManagement.WPF.Utilities;
using FamilyTreeManagement.WPF.Services;
using Serilog;

namespace FamilyTreeManagement.WPF
{
    public partial class App : System.Windows.Application
    {
        private IHost? _host;
        private ILogger<App>? _logger;
        private static bool _isStarting = false;

        protected override async void OnStartup(StartupEventArgs e)
        {
            // Prevent double startup
            if (_isStarting)
            {
                Log.Warning("Application startup already in progress, ignoring duplicate startup call");
                return;
            }
            _isStarting = true;

            // Add global exception handlers to catch any unhandled exceptions
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
            DispatcherUnhandledException += OnDispatcherUnhandledException;
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

            try
            {
                // Configure Serilog first - write to project root directory
                var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "logs", "familytree-.txt");
                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Debug()
                    .WriteTo.Console()
                    .WriteTo.File(logPath, rollingInterval: RollingInterval.Day)
                    .CreateLogger();

                Log.Information("=== Family Tree Management Application Starting ===");
                Log.Information("Current Directory: {CurrentDirectory}", Environment.CurrentDirectory);
                Log.Information("Application Arguments: {Args}", string.Join(" ", e.Args));

                Log.Information("Creating host builder...");
                var builder = Host.CreateApplicationBuilder();

                // Add Serilog
                Log.Information("Adding Serilog to services...");
                builder.Services.AddSerilog();

                // Add configuration - look for appsettings.json in the application directory
                Log.Information("Loading configuration...");
                var appSettingsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
                Log.Information("Looking for appsettings.json at: {AppSettingsPath}", appSettingsPath);

                if (!File.Exists(appSettingsPath))
                {
                    Log.Warning("appsettings.json not found at {AppSettingsPath}, trying current directory", appSettingsPath);
                    appSettingsPath = "appsettings.json";
                }

                builder.Configuration.AddJsonFile(appSettingsPath, optional: false, reloadOnChange: true);
                Log.Information("Configuration loaded successfully from: {AppSettingsPath}", appSettingsPath);

                // Add services
                Log.Information("Registering application services...");
                builder.Services.AddApplication();
                Log.Information("Application services registered");

                Log.Information("Registering infrastructure services...");
                builder.Services.AddInfrastructure(builder.Configuration);
                Log.Information("Infrastructure services registered");

                // Add Localization
                Log.Information("Registering localization services...");
                builder.Services.AddLocalization();
                builder.Services.AddSingleton<LocalizationService>();
                Log.Information("Localization services registered");

                // Add ViewModels
                Log.Information("Registering ViewModels...");
                builder.Services.AddTransient<MainViewModel>();
                builder.Services.AddTransient<RegisterFamilyViewModel>();
                builder.Services.AddTransient<AssignParentsViewModel>();
                builder.Services.AddTransient<AddPersonViewModel>();
                Log.Information("ViewModels registered");

                Log.Information("Building host...");
                _host = builder.Build();
                _logger = _host.Services.GetRequiredService<ILogger<App>>();

                _logger.LogInformation("Host built successfully");

                // Set Arabic as default culture
                Log.Information("Setting Arabic as default culture...");
                var localizationService = _host.Services.GetRequiredService<LocalizationService>();
                localizationService.SetArabic();
                _logger.LogInformation("Arabic culture set as default");

                // Ensure database is created
                await EnsureDatabaseCreated();

                // Start the main window
                var mainWindow = new MainWindow
                {
                    DataContext = _host.Services.GetRequiredService<MainViewModel>()
                };

                _logger.LogInformation("Main window created");
                mainWindow.Show();
                _logger.LogInformation("Main window shown");

                // Don't call base.OnStartup(e) since we removed StartupUri and handle window creation manually
            }
            catch (Exception ex)
            {
                _isStarting = false; // Reset flag on failure
                Log.Fatal(ex, "Application failed to start");
                MessageBox.Show($"Application failed to start: {ex.Message}\n\nDetails: {ex}",
                    "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(1);
            }
        }

        private async Task EnsureDatabaseCreated()
        {
            try
            {
                _logger?.LogInformation("Starting database setup...");

                var configuration = _host!.Services.GetRequiredService<IConfiguration>();

                // First test basic connection
                _logger?.LogInformation("Testing basic database connection...");
                var connectionTest = await DatabaseConnectionTester.TestConnectionAsync(configuration, _logger);

                if (!connectionTest)
                {
                    _logger?.LogError("Basic database connection test failed");
                    return; // Error already shown by the tester
                }

                // Try to create database if it doesn't exist
                _logger?.LogInformation("Ensuring database exists...");
                await DatabaseConnectionTester.CreateDatabaseIfNotExistsAsync(configuration, _logger);

                // Now test with Entity Framework
                _logger?.LogInformation("Testing Entity Framework connection...");
                using var scope = _host.Services.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<Infrastructure.Data.FamilyTreeContext>();

                var canConnect = await context.Database.CanConnectAsync();
                _logger?.LogInformation("Entity Framework can connect: {CanConnect}", canConnect);

                if (!canConnect)
                {
                    throw new InvalidOperationException("Entity Framework cannot connect to the database after basic connection test passed.");
                }

                _logger?.LogInformation("Creating/updating database schema...");
                await context.Database.EnsureCreatedAsync();
                _logger?.LogInformation("Database setup completed successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Database setup failed");
                Log.Error(ex, "Database setup failed");

                var message = $"Database setup failed: {ex.Message}\n\n" +
                             $"Please check the logs folder for detailed error information.\n\n" +
                             $"Common solutions:\n" +
                             $"1. Ensure MySQL is running\n" +
                             $"2. Check connection string in appsettings.json\n" +
                             $"3. Verify database user permissions\n" +
                             $"4. Check if port 3306 is accessible\n\n" +
                             $"Connection string: {GetSafeConnectionString(_host.Services.GetRequiredService<IConfiguration>())}";

                MessageBox.Show(message, "Database Setup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                throw; // Re-throw to stop application startup
            }
        }

        private static string GetSafeConnectionString(IConfiguration configuration)
        {
            try
            {
                var connectionString = configuration.GetConnectionString("DefaultConnection");
                if (string.IsNullOrEmpty(connectionString))
                    return "Not found";

                var builder = new MySqlConnector.MySqlConnectionStringBuilder(connectionString);
                var originalPassword = builder.Password;
                builder.Password = string.IsNullOrEmpty(originalPassword) ? "" : "***";
                return builder.ConnectionString;
            }
            catch
            {
                return "Error reading connection string";
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            try
            {
                _isStarting = false; // Reset flag on exit
                _logger?.LogInformation("Application shutting down...");
                Log.Information("Application shutting down...");

                if (_host != null)
                {
                    await _host.StopAsync();
                    _host.Dispose();
                }

                Log.CloseAndFlush();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error during application shutdown");
            }
            finally
            {
                base.OnExit(e);
            }
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var ex = e.ExceptionObject as Exception;
            Log.Fatal(ex, "Unhandled exception occurred. IsTerminating: {IsTerminating}", e.IsTerminating);

            if (ex != null)
            {
                MessageBox.Show($"Unhandled Exception: {ex.Message}\n\nDetails: {ex}",
                    "Critical Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            Log.Fatal(e.Exception, "Unhandled dispatcher exception occurred");

            // Don't handle startup exceptions - let them crash the app properly
            if (e.Exception is System.Windows.Markup.XamlParseException)
            {
                Log.Fatal("XAML parsing error during startup - allowing application to terminate");
                MessageBox.Show($"XAML Error: {e.Exception.Message}\n\nApplication will close.",
                    "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return; // Don't set e.Handled = true, let the app crash
            }

            MessageBox.Show($"Dispatcher Exception: {e.Exception.Message}\n\nDetails: {e.Exception}",
                "Critical Error", MessageBoxButton.OK, MessageBoxImage.Error);

            e.Handled = true; // Only prevent crashing for non-startup exceptions
        }

        private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            Log.Fatal(e.Exception, "Unobserved task exception occurred");

            MessageBox.Show($"Task Exception: {e.Exception.Message}\n\nDetails: {e.Exception}",
                "Critical Error", MessageBoxButton.OK, MessageBoxImage.Error);

            e.SetObserved(); // Prevent application from crashing
        }
    }
}
