using FamilyTreeManagement.Domain.Enums;

namespace FamilyTreeManagement.Domain.Entities
{
    public class Person
    {
        public int Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public DateTime? DateOfDeath { get; set; }
        public Gender Gender { get; set; }
        public string? PlaceOfBirth { get; set; }
        public string? Occupation { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<PersonRelation> RelationsAsParent { get; set; } = new List<PersonRelation>();
        public virtual ICollection<PersonRelation> RelationsAsChild { get; set; } = new List<PersonRelation>();
        public virtual ICollection<Family> FamiliesAsFather { get; set; } = new List<Family>();
        public virtual ICollection<Family> FamiliesAsMother { get; set; } = new List<Family>();

        public string FullName => $"{FirstName} {LastName}";
        public int? Age => DateOfBirth.HasValue ? 
            (DateOfDeath ?? DateTime.Now).Year - DateOfBirth.Value.Year : null;
        public bool IsAlive => !DateOfDeath.HasValue;
    }
}
