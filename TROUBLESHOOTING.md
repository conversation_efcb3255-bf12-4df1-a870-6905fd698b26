# Family Tree Management - Troubleshooting Guide

## Application Starts Then Disappears

This is the most common issue and is usually related to database connectivity. The application now includes comprehensive logging to help diagnose the problem.

### Step 1: Check the Logs

The application creates detailed logs in the `logs/` folder in the application directory. Look for files named `familytree-YYYY-MM-DD.txt`.

**Log file location:** `FamilyTreeManagement.WPF/bin/Debug/net8.0-windows/logs/`

### Step 2: Common Issues and Solutions

#### 1. MySQL Not Running
**Error:** "Cannot connect to MySQL server"
**Solution:** 
- Start MySQL service
- Windows: `net start mysql` or use MySQL Workbench
- Check if MySQL is running on port 3306

#### 2. Database Doesn't Exist
**Error:** "Database does not exist"
**Solution:** 
- The application will try to create the database automatically
- If it fails, manually create it:
```sql
CREATE DATABASE FamilyTreeDB;
```

#### 3. Wrong Password
**Error:** "Access denied"
**Solution:** 
- Update the password in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=FamilyTreeDB;Uid=root;Pwd=YOUR_ACTUAL_PASSWORD;"
  }
}
```

#### 4. MySQL Not Installed
**Error:** Various connection errors
**Solution:** 
- Install MySQL Server 8.0 or higher
- Download from: https://dev.mysql.com/downloads/mysql/

### Step 3: Test Database Connection Manually

You can test the connection using MySQL command line:

```bash
mysql -u root -p -h localhost
```

If this works, the issue is likely in the connection string format.

### Step 4: Connection String Format

The connection string should be in this format:
```
Server=localhost;Database=FamilyTreeDB;Uid=root;Pwd=your_password;
```

**Common mistakes:**
- Using `User` instead of `Uid`
- Using `Password` instead of `Pwd`
- Missing semicolon at the end
- Wrong server name (should be `localhost` for local MySQL)

### Step 5: Check Windows Event Logs

If the application crashes without showing an error:
1. Open Event Viewer (Windows + R, type `eventvwr`)
2. Go to Windows Logs > Application
3. Look for errors from the application

### Step 6: Run from Command Line

To see more detailed error information:

```bash
cd FamilyTreeManagement.WPF
dotnet run
```

This will show any startup errors in the console.

### Step 7: Verify .NET 8.0 Installation

Make sure you have .NET 8.0 runtime installed:
```bash
dotnet --version
```

Should show version 8.0.x or higher.

## Error Messages and Solutions

### "Application failed to start"
- Check the detailed error message in the popup
- Look at the log files for more information
- Verify all prerequisites are installed

### "Database setup failed"
- MySQL is not running
- Connection string is incorrect
- Database permissions issue
- Port 3306 is blocked by firewall

### "Entity Framework cannot connect"
- EF Core specific issue
- Usually related to connection string format
- Check if the database was created properly

## Quick Setup Checklist

1. ✅ MySQL Server 8.0+ installed and running
2. ✅ .NET 8.0 SDK/Runtime installed
3. ✅ Connection string updated in `appsettings.json`
4. ✅ Database user has CREATE/ALTER permissions
5. ✅ Port 3306 is accessible
6. ✅ No antivirus blocking the application

## Getting Help

If you're still having issues:

1. **Check the log files** in the `logs/` folder
2. **Run the application from command line** to see console output
3. **Test MySQL connection** manually using MySQL client
4. **Verify connection string** format and credentials

## Sample Working Configuration

Here's a sample `appsettings.json` that works with a default MySQL installation:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=FamilyTreeDB;Uid=root;Pwd=;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.EntityFrameworkCore": "Information",
      "FamilyTreeManagement": "Debug"
    }
  }
}
```

**Note:** Empty password (`Pwd=;`) works if your MySQL root user has no password set.
