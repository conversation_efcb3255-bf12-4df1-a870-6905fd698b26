using Microsoft.EntityFrameworkCore;
using FamilyTreeManagement.Domain.Entities;
using FamilyTreeManagement.Domain.Enums;
using FamilyTreeManagement.Domain.Interfaces;
using FamilyTreeManagement.Infrastructure.Data;

namespace FamilyTreeManagement.Infrastructure.Repositories
{
    public class PersonRelationRepository : IPersonRelationRepository
    {
        private readonly FamilyTreeContext _context;

        public PersonRelationRepository(FamilyTreeContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<PersonRelation>> GetAllAsync()
        {
            return await _context.PersonRelations
                .Include(r => r.Parent)
                .Include(r => r.Child)
                .ToListAsync();
        }

        public async Task<PersonRelation?> GetByIdAsync(int id)
        {
            return await _context.PersonRelations
                .Include(r => r.Parent)
                .Include(r => r.Child)
                .FirstOrDefaultAsync(r => r.Id == id);
        }

        public async Task<IEnumerable<PersonRelation>> GetRelationsByPersonAsync(int personId)
        {
            return await _context.PersonRelations
                .Include(r => r.Parent)
                .Include(r => r.Child)
                .Where(r => r.ParentId == personId || r.ChildId == personId)
                .ToListAsync();
        }

        public async Task<IEnumerable<PersonRelation>> GetRelationsByParentAsync(int parentId)
        {
            return await _context.PersonRelations
                .Include(r => r.Child)
                .Where(r => r.ParentId == parentId)
                .ToListAsync();
        }

        public async Task<IEnumerable<PersonRelation>> GetRelationsByChildAsync(int childId)
        {
            return await _context.PersonRelations
                .Include(r => r.Parent)
                .Where(r => r.ChildId == childId)
                .ToListAsync();
        }

        public async Task<PersonRelation?> GetRelationAsync(int parentId, int childId, RelationType relationType)
        {
            return await _context.PersonRelations
                .Include(r => r.Parent)
                .Include(r => r.Child)
                .FirstOrDefaultAsync(r => r.ParentId == parentId && r.ChildId == childId && r.RelationType == relationType);
        }

        public async Task<PersonRelation> AddAsync(PersonRelation relation)
        {
            _context.PersonRelations.Add(relation);
            await _context.SaveChangesAsync();
            return relation;
        }

        public async Task<PersonRelation> UpdateAsync(PersonRelation relation)
        {
            _context.Entry(relation).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return relation;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var relation = await _context.PersonRelations.FindAsync(id);
            if (relation == null) return false;

            _context.PersonRelations.Remove(relation);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteRelationAsync(int parentId, int childId, RelationType relationType)
        {
            var relation = await GetRelationAsync(parentId, childId, relationType);
            if (relation == null) return false;

            _context.PersonRelations.Remove(relation);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int parentId, int childId, RelationType relationType)
        {
            return await _context.PersonRelations
                .AnyAsync(r => r.ParentId == parentId && r.ChildId == childId && r.RelationType == relationType);
        }
    }
}
