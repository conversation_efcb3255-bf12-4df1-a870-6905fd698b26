using FamilyTreeManagement.Domain.Entities;

namespace FamilyTreeManagement.Domain.Interfaces
{
    public interface IFamilyRepository
    {
        Task<IEnumerable<Family>> GetAllAsync();
        Task<Family?> GetByIdAsync(int id);
        Task<Family?> GetByIdWithMembersAsync(int id);
        Task<IEnumerable<Family>> GetFamiliesByPersonAsync(int personId);
        Task<Family> AddAsync(Family family);
        Task<Family> UpdateAsync(Family family);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
    }
}
