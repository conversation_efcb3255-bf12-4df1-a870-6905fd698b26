using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FamilyTreeManagement.Application.Services;
using FamilyTreeManagement.Application.DTOs;
using FamilyTreeManagement.WPF.Services;
using System.Collections.ObjectModel;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Windows;

namespace FamilyTreeManagement.WPF.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly IPersonService _personService;
        private readonly IFamilyService _familyService;
        private readonly IServiceProvider _serviceProvider;

        public LocalizationService LocalizationService { get; }

        [ObservableProperty]
        private ObservableCollection<PersonDto> persons = new();

        [ObservableProperty]
        private ObservableCollection<FamilyDto> families = new();

        [ObservableProperty]
        private PersonDto? selectedPerson;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private bool isRegisterFamilyVisible = false;

        [ObservableProperty]
        private bool isAssignParentsVisible = false;

        [ObservableProperty]
        private bool isAddPersonVisible = false;

        [ObservableProperty]
        private RegisterFamilyViewModel? registerFamilyViewModel;

        [ObservableProperty]
        private AssignParentsViewModel? assignParentsViewModel;

        [ObservableProperty]
        private AddPersonViewModel? addPersonViewModel;

        public MainViewModel(IPersonService personService, IFamilyService familyService, LocalizationService localizationService, IServiceProvider serviceProvider)
        {
            _personService = personService;
            _familyService = familyService;
            LocalizationService = localizationService;
            _serviceProvider = serviceProvider;
        }

        [RelayCommand]
        private async Task LoadDataAsync()
        {
            try
            {
                var personsData = await _personService.GetAllPersonsAsync();
                Persons.Clear();
                foreach (var person in personsData)
                {
                    Persons.Add(person);
                }

                var familiesData = await _familyService.GetAllFamiliesAsync();
                Families.Clear();
                foreach (var family in familiesData)
                {
                    Families.Add(family);
                }
            }
            catch (Exception ex)
            {
                // Handle error - could show message box or status
                System.Windows.MessageBox.Show($"Error loading data: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task SearchPersonsAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadDataAsync();
                return;
            }

            try
            {
                var searchResults = await _personService.SearchPersonsByNameAsync(SearchText);
                Persons.Clear();
                foreach (var person in searchResults)
                {
                    Persons.Add(person);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error searching: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ShowRegisterFamily()
        {
            try
            {
                // Reset all visibility flags first
                IsAddPersonVisible = false;
                IsRegisterFamilyVisible = false;
                IsAssignParentsVisible = false;

                // Create the ViewModel using DI
                if(RegisterFamilyViewModel is not null) RegisterFamilyViewModel.FamilyRegistered -= OnFamilyRegistered;
                RegisterFamilyViewModel = _serviceProvider.GetRequiredService<RegisterFamilyViewModel>();
                RegisterFamilyViewModel.FamilyRegistered += OnFamilyRegistered;

                // Set the visibility
                IsRegisterFamilyVisible = true;

                System.Diagnostics.Debug.WriteLine("🎯 ShowRegisterFamily: RegisterFamilyViewModel created and visibility set");
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error showing Register Family view: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ShowAssignParents()
        {
            if (SelectedPerson == null)
            {
                System.Windows.MessageBox.Show("Please select a person first.", "No Person Selected",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                return;
            }

            AssignParentsViewModel = new AssignParentsViewModel(_personService, SelectedPerson);
            AssignParentsViewModel.ParentsAssigned += OnParentsAssigned;
            IsAssignParentsVisible = true;
            IsRegisterFamilyVisible = false;
            IsAddPersonVisible = false;
        }

        [RelayCommand]
        private void ShowAddPerson()
        {
            try
            {
                // Reset all visibility flags first
                IsAddPersonVisible = false;
                IsRegisterFamilyVisible = false;
                IsAssignParentsVisible = false;

                // Create the ViewModel with logger
                var app = (App)System.Windows.Application.Current;
                var logger = app.Host.Services.GetRequiredService<ILogger<AddPersonViewModel>>();
                AddPersonViewModel = new AddPersonViewModel(_personService, logger);
                AddPersonViewModel.PersonAdded += OnPersonAdded;

                // Set the visibility
                IsAddPersonVisible = true;
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error showing Add Person view: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void HideViews()
        {
            IsRegisterFamilyVisible = false;
            IsAssignParentsVisible = false;
            IsAddPersonVisible = false;
        }

        private async void OnFamilyRegistered()
        {
            IsRegisterFamilyVisible = false;
            await LoadDataAsync();
        }

        private async void OnParentsAssigned()
        {
            IsAssignParentsVisible = false;
            await LoadDataAsync();
        }

        private async void OnPersonAdded()
        {
            // DON'T hide the view - keep it open for adding more persons
            // IsAddPersonVisible = false;  // REMOVED - this was hiding the view

            // Just refresh the data to show the new person in the list
            await LoadDataAsync();
        }

        partial void OnSelectedPersonChanged(PersonDto? value)
        {
            // Could load additional details when person is selected
        }

        [RelayCommand]
        private void SwitchToArabic()
        {
            LocalizationService.SetArabic();
        }

        [RelayCommand]
        private void SwitchToEnglish()
        {
            LocalizationService.SetEnglish();
        }
    }
}
