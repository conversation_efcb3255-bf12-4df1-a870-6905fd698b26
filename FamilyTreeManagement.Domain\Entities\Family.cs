namespace FamilyTreeManagement.Domain.Entities
{
    public class Family
    {
        public int Id { get; set; }
        public string FamilyName { get; set; } = string.Empty;
        public int? FatherId { get; set; }
        public int? MotherId { get; set; }
        public DateTime? MarriageDate { get; set; }
        public string? MarriagePlace { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        public string? Notes { get; set; }

        // Navigation properties
        public virtual Person? Father { get; set; }
        public virtual Person? Mother { get; set; }
        public virtual ICollection<Person> Children { get; set; } = new List<Person>();
    }
}
