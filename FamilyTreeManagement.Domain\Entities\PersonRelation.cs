using FamilyTreeManagement.Domain.Enums;

namespace FamilyTreeManagement.Domain.Entities
{
    public class PersonRelation
    {
        public int Id { get; set; }
        public int ParentId { get; set; }
        public int ChildId { get; set; }
        public RelationType RelationType { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string? Notes { get; set; }

        // Navigation properties
        public virtual Person Parent { get; set; } = null!;
        public virtual Person Child { get; set; } = null!;
    }
}
