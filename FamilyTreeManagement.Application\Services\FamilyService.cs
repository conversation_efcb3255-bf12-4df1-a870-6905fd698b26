using AutoMapper;
using FamilyTreeManagement.Application.DTOs;
using FamilyTreeManagement.Domain.Entities;
using FamilyTreeManagement.Domain.Enums;
using FamilyTreeManagement.Domain.Interfaces;

namespace FamilyTreeManagement.Application.Services
{
    public interface IFamilyService
    {
        Task<IEnumerable<FamilyDto>> GetAllFamiliesAsync();
        Task<FamilyDto?> GetFamilyByIdAsync(int id);
        Task<FamilyDto> CreateFamilyAsync(CreateFamilyDto createFamilyDto);
        Task<bool> DeleteFamilyAsync(int id);
    }

    public class FamilyService : IFamilyService
    {
        private readonly IFamilyRepository _familyRepository;
        private readonly IPersonRepository _personRepository;
        private readonly IPersonRelationRepository _relationRepository;
        private readonly IMapper _mapper;

        public FamilyService(
            IFamilyRepository familyRepository,
            IPersonRepository personRepository,
            IPersonRelationRepository relationRepository,
            IMapper mapper)
        {
            _familyRepository = familyRepository;
            _personRepository = personRepository;
            _relationRepository = relationRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<FamilyDto>> GetAllFamiliesAsync()
        {
            var families = await _familyRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<FamilyDto>>(families);
        }

        public async Task<FamilyDto?> GetFamilyByIdAsync(int id)
        {
            var family = await _familyRepository.GetByIdWithMembersAsync(id);
            return family != null ? _mapper.Map<FamilyDto>(family) : null;
        }

        public async Task<FamilyDto> CreateFamilyAsync(CreateFamilyDto createFamilyDto)
        {
            var family = new Family
            {
                FamilyName = createFamilyDto.FamilyName,
                MarriageDate = createFamilyDto.MarriageDate,
                MarriagePlace = createFamilyDto.MarriagePlace,
                Notes = createFamilyDto.Notes
            };

            // Create father if provided
            if (createFamilyDto.Father != null)
            {
                var father = _mapper.Map<Person>(createFamilyDto.Father);
                father.Gender = Gender.Male;
                var createdFather = await _personRepository.AddAsync(father);
                family.FatherId = createdFather.Id;
            }

            // Create mother if provided
            if (createFamilyDto.Mother != null)
            {
                var mother = _mapper.Map<Person>(createFamilyDto.Mother);
                mother.Gender = Gender.Female;
                var createdMother = await _personRepository.AddAsync(mother);
                family.MotherId = createdMother.Id;
            }

            // Create family
            var createdFamily = await _familyRepository.AddAsync(family);

            // Create children and establish relationships
            foreach (var childDto in createFamilyDto.Children)
            {
                var child = _mapper.Map<Person>(childDto);
                var createdChild = await _personRepository.AddAsync(child);

                // Create father-child relationship
                if (family.FatherId.HasValue)
                {
                    var fatherRelation = new PersonRelation
                    {
                        ParentId = family.FatherId.Value,
                        ChildId = createdChild.Id,
                        RelationType = RelationType.Father
                    };
                    await _relationRepository.AddAsync(fatherRelation);
                }

                // Create mother-child relationship
                if (family.MotherId.HasValue)
                {
                    var motherRelation = new PersonRelation
                    {
                        ParentId = family.MotherId.Value,
                        ChildId = createdChild.Id,
                        RelationType = RelationType.Mother
                    };
                    await _relationRepository.AddAsync(motherRelation);
                }
            }

            // Return the created family with all members
            var familyWithMembers = await _familyRepository.GetByIdWithMembersAsync(createdFamily.Id);
            return _mapper.Map<FamilyDto>(familyWithMembers);
        }

        public async Task<bool> DeleteFamilyAsync(int id)
        {
            return await _familyRepository.DeleteAsync(id);
        }
    }
}
