<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Main Window -->
  <data name="AppTitle" xml:space="preserve">
    <value>إدارة شجرة العائلة</value>
  </data>
  <data name="AddPerson" xml:space="preserve">
    <value>إضافة شخص</value>
  </data>
  <data name="RegisterFamily" xml:space="preserve">
    <value>تسجيل عائلة</value>
  </data>
  <data name="AssignParents" xml:space="preserve">
    <value>تعيين الوالدين</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>تحديث</value>
  </data>
  <data name="SearchPersons" xml:space="preserve">
    <value>البحث عن الأشخاص...</value>
  </data>
  <data name="TotalPersons" xml:space="preserve">
    <value>إجمالي الأشخاص: {0}</value>
  </data>
  <data name="Families" xml:space="preserve">
    <value>العائلات</value>
  </data>

  <!-- Add Person Form -->
  <data name="AddNewPerson" xml:space="preserve">
    <value>إضافة شخص جديد</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>الاسم الأول</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>اسم العائلة</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>الجنس</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>ذكر</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>أنثى</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>آخر</value>
  </data>
  <data name="DateOfBirth" xml:space="preserve">
    <value>تاريخ الميلاد</value>
  </data>
  <data name="PlaceOfBirth" xml:space="preserve">
    <value>مكان الميلاد</value>
  </data>
  <data name="Occupation" xml:space="preserve">
    <value>المهنة</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>مسح</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>إغلاق</value>
  </data>

  <!-- Messages -->
  <data name="Success" xml:space="preserve">
    <value>نجح</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="PersonAddedSuccessfully" xml:space="preserve">
    <value>تم إضافة الشخص بنجاح!</value>
  </data>
  <data name="FormCleared" xml:space="preserve">
    <value>تم مسح النموذج!</value>
  </data>
  <data name="MissingRequiredFields" xml:space="preserve">
    <value>يرجى إدخال الاسم الأول واسم العائلة.</value>
  </data>
  <data name="MissingGender" xml:space="preserve">
    <value>يرجى اختيار الجنس.</value>
  </data>

  <!-- Register Family -->
  <data name="RegisterNewFamily" xml:space="preserve">
    <value>تسجيل عائلة جديدة</value>
  </data>
  <data name="FamilyName" xml:space="preserve">
    <value>اسم العائلة</value>
  </data>
  <data name="Father" xml:space="preserve">
    <value>الأب</value>
  </data>
  <data name="Mother" xml:space="preserve">
    <value>الأم</value>
  </data>
  <data name="MarriageDate" xml:space="preserve">
    <value>تاريخ الزواج</value>
  </data>
  <data name="RegisterFamily" xml:space="preserve">
    <value>تسجيل عائلة</value>
  </data>

  <!-- Assign Parents -->
  <data name="AssignParentsTitle" xml:space="preserve">
    <value>تعيين الوالدين</value>
  </data>
  <data name="SelectPerson" xml:space="preserve">
    <value>اختيار شخص</value>
  </data>
  <data name="SearchAvailablePersons" xml:space="preserve">
    <value>البحث عن الأشخاص المتاحين...</value>
  </data>
  <data name="AssignParentsButton" xml:space="preserve">
    <value>تعيين الوالدين</value>
  </data>

  <!-- Common -->
  <data name="Save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="Age" xml:space="preserve">
    <value>العمر: {0}</value>
  </data>
  <data name="PersonInformation" xml:space="preserve">
    <value>معلومات الشخص</value>
  </data>
  <data name="SelectGender" xml:space="preserve">
    <value>اختر الجنس</value>
  </data>

  <!-- Additional Register Family Keys -->
  <data name="FamilyInformation" xml:space="preserve">
    <value>معلومات العائلة</value>
  </data>
  <data name="FatherInformation" xml:space="preserve">
    <value>معلومات الأب</value>
  </data>
  <data name="MotherInformation" xml:space="preserve">
    <value>معلومات الأم</value>
  </data>
  <data name="ChildrenInformation" xml:space="preserve">
    <value>معلومات الأطفال</value>
  </data>
  <data name="MarriagePlace" xml:space="preserve">
    <value>مكان الزواج</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>إضافة</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>إزالة</value>
  </data>

  <!-- Additional Assign Parents Keys -->
  <data name="SelectedPerson" xml:space="preserve">
    <value>الشخص المحدد</value>
  </data>
  <data name="SelectedParents" xml:space="preserve">
    <value>الوالدان المحددان</value>
  </data>
  <data name="SearchByName" xml:space="preserve">
    <value>البحث بالاسم...</value>
  </data>
  <data name="SetAsFather" xml:space="preserve">
    <value>تعيين كأب</value>
  </data>
  <data name="SetAsMother" xml:space="preserve">
    <value>تعيين كأم</value>
  </data>
  <data name="NoFatherSelected" xml:space="preserve">
    <value>لم يتم اختيار أب</value>
  </data>
  <data name="NoMotherSelected" xml:space="preserve">
    <value>لم يتم اختيار أم</value>
  </data>
  <data name="ClearSelection" xml:space="preserve">
    <value>مسح التحديد</value>
  </data>
</root>
