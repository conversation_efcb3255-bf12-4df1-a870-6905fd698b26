using System.Windows.Controls;
using System.Windows;
using Microsoft.Extensions.Logging;
using FamilyTreeManagement.WPF.ViewModels;

namespace FamilyTreeManagement.WPF.Views
{
    public partial class AddPersonView : UserControl
    {
        public AddPersonView()
        {
            InitializeComponent();
            //App.Logger.information("AddPersonView created");
            //GetDataContext().Logger.LogInformation("AddPersonView created");
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // Find the main window and call HideViews command

            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow?.DataContext is ViewModels.MainViewModel mainViewModel)
            {
                mainViewModel.HideViewsCommand.Execute(null);
            }
        }

        private AddPersonViewModel? GetDataContext() {
            if(DataContext is null) return null;
            if(DataContext is AddPersonViewModel addPersonViewModel) return addPersonViewModel;
            return null;
        }
    }
}
