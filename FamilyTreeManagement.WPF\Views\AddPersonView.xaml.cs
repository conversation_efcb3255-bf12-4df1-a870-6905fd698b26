using System.Windows.Controls;
using System.Windows;

namespace FamilyTreeManagement.WPF.Views
{
    public partial class AddPersonView : UserControl
    {
        public AddPersonView()
        {
            InitializeComponent();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // Find the main window and call HideViews command
            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow?.DataContext is ViewModels.MainViewModel mainViewModel)
            {
                mainViewModel.HideViewsCommand.Execute(null);
            }
        }
    }
}
