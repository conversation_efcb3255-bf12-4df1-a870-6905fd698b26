using Microsoft.Extensions.Localization;
using System.ComponentModel;
using System.Globalization;
using System.Runtime.CompilerServices;

namespace FamilyTreeManagement.WPF.Services
{
    public class LocalizationService : INotifyPropertyChanged
    {
        private readonly IStringLocalizer<LocalizationService> _localizer;

        public LocalizationService(IStringLocalizer<LocalizationService> localizer)
        {
            _localizer = localizer;
        }

        public string this[string key] => _localizer[key];

        public string GetString(string key) => _localizer[key];

        public string GetString(string key, params object[] arguments) => _localizer[key, arguments];

        public void SetCulture(string cultureCode)
        {
            var culture = new CultureInfo(cultureCode);
            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;

            // Notify all bindings that the culture has changed
            OnPropertyChanged(string.Empty);
        }

        public void SetArabic() => SetCulture("ar");
        public void SetEnglish() => SetCulture("en");

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // Commonly used strings as properties for easier binding
        public string AppTitle => GetString("AppTitle");
        public string AddPerson => GetString("AddPerson");
        public string RegisterFamily => GetString("RegisterFamily");
        public string AssignParents => GetString("AssignParents");
        public string Refresh => GetString("Refresh");
        public string SearchPersons => GetString("SearchPersons");
        public string Families => GetString("Families");
        public string AddNewPerson => GetString("AddNewPerson");
        public string FirstName => GetString("FirstName");
        public string LastName => GetString("LastName");
        public string Gender => GetString("Gender");
        public string Male => GetString("Male");
        public string Female => GetString("Female");
        public string Other => GetString("Other");
        public string DateOfBirth => GetString("DateOfBirth");
        public string PlaceOfBirth => GetString("PlaceOfBirth");
        public string Occupation => GetString("Occupation");
        public string Clear => GetString("Clear");
        public string Close => GetString("Close");
        public string Success => GetString("Success");
        public string Error => GetString("Error");
        public string PersonAddedSuccessfully => GetString("PersonAddedSuccessfully");
        public string FormCleared => GetString("FormCleared");
        public string MissingRequiredFields => GetString("MissingRequiredFields");
        public string MissingGender => GetString("MissingGender");

        // Additional properties for the new form
        public string PersonInformation => GetString("PersonInformation");
        public string SelectGender => GetString("SelectGender");

        // Helper method to get localized string with fallback
        public string GetLocalizedString(string key, string fallback = "")
        {
            try
            {
                var result = GetString(key);
                return string.IsNullOrEmpty(result) ? fallback : result;
            }
            catch
            {
                return fallback;
            }
        }
    }
}
