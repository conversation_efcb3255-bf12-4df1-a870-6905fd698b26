using System.Windows;
using FamilyTreeManagement.Application.Services;
using FamilyTreeManagement.Application.DTOs;
using FamilyTreeManagement.Domain.Enums;
using Microsoft.Extensions.DependencyInjection;
using System.Windows.Controls;

namespace FamilyTreeManagement.WPF.Windows
{
    public partial class AddPersonWindow : Window
    {
        private readonly IPersonService _personService;
        public bool PersonAdded { get; private set; } = false;

        public AddPersonWindow(IPersonService personService)
        {
            InitializeComponent();
            _personService = personService;
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            FirstNameTextBox.Text = "";
            LastNameTextBox.Text = "";
            GenderComboBox.SelectedIndex = 0;
            DateOfBirthPicker.SelectedDate = null;
            PlaceOfBirthTextBox.Text = "";
            OccupationTextBox.Text = "";
            
            MessageBox.Show("🧹 Form cleared!", "Form Cleared", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void AddPersonButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validation
                if (string.IsNullOrWhiteSpace(FirstNameTextBox.Text) || string.IsNullOrWhiteSpace(LastNameTextBox.Text))
                {
                    MessageBox.Show("❌ Please enter both First Name and Last Name.", "Missing Required Fields", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (GenderComboBox.SelectedItem == null)
                {
                    MessageBox.Show("❌ Please select a Gender.", "Missing Required Field", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Get gender
                var selectedItem = (ComboBoxItem)GenderComboBox.SelectedItem;
                var genderValue = int.Parse(selectedItem.Tag.ToString());
                var gender = (Gender)genderValue;

                var createPersonDto = new CreatePersonDto
                {
                    FirstName = FirstNameTextBox.Text.Trim(),
                    LastName = LastNameTextBox.Text.Trim(),
                    DateOfBirth = DateOfBirthPicker.SelectedDate,
                    Gender = gender,
                    PlaceOfBirth = PlaceOfBirthTextBox.Text?.Trim(),
                    Occupation = OccupationTextBox.Text?.Trim()
                };

                await _personService.CreatePersonAsync(createPersonDto);

                var successMessage = $"✅ SUCCESS!\n\n" +
                                   $"Person Added Successfully:\n" +
                                   $"• Name: {FirstNameTextBox.Text} {LastNameTextBox.Text}\n" +
                                   $"• Gender: {selectedItem.Content}\n" +
                                   $"• Date of Birth: {(DateOfBirthPicker.SelectedDate?.ToString("yyyy-MM-dd") ?? "Not specified")}\n" +
                                   $"• Place of Birth: {(string.IsNullOrEmpty(PlaceOfBirthTextBox.Text) ? "Not specified" : PlaceOfBirthTextBox.Text)}\n" +
                                   $"• Occupation: {(string.IsNullOrEmpty(OccupationTextBox.Text) ? "Not specified" : OccupationTextBox.Text)}\n\n" +
                                   $"The person will appear in the main window.";

                MessageBox.Show(successMessage, "Person Added Successfully!", 
                    MessageBoxButton.OK, MessageBoxImage.Information);

                PersonAdded = true;
                ClearButton_Click(sender, e); // Clear the form for next person
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ ERROR: {ex.Message}", "Error Adding Person", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
