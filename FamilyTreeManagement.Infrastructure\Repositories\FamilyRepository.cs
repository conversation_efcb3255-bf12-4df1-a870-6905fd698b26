using Microsoft.EntityFrameworkCore;
using FamilyTreeManagement.Domain.Entities;
using FamilyTreeManagement.Domain.Interfaces;
using FamilyTreeManagement.Infrastructure.Data;

namespace FamilyTreeManagement.Infrastructure.Repositories
{
    public class FamilyRepository : IFamilyRepository
    {
        private readonly FamilyTreeContext _context;

        public FamilyRepository(FamilyTreeContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Family>> GetAllAsync()
        {
            return await _context.Families
                .Include(f => f.Father)
                .Include(f => f.Mother)
                .OrderBy(f => f.FamilyName)
                .ToListAsync();
        }

        public async Task<Family?> GetByIdAsync(int id)
        {
            return await _context.Families
                .Include(f => f.Father)
                .Include(f => f.Mother)
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        public async Task<Family?> GetByIdWithMembersAsync(int id)
        {
            return await _context.Families
                .Include(f => f.Father)
                .Include(f => f.<PERSON>)
                .Include(f => f.Children)
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        public async Task<IEnumerable<Family>> GetFamiliesByPersonAsync(int personId)
        {
            return await _context.Families
                .Include(f => f.Father)
                .Include(f => f.Mother)
                .Where(f => f.FatherId == personId || f.MotherId == personId)
                .ToListAsync();
        }

        public async Task<Family> AddAsync(Family family)
        {
            _context.Families.Add(family);
            await _context.SaveChangesAsync();
            return family;
        }

        public async Task<Family> UpdateAsync(Family family)
        {
            family.UpdatedAt = DateTime.UtcNow;
            _context.Entry(family).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return family;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var family = await _context.Families.FindAsync(id);
            if (family == null) return false;

            _context.Families.Remove(family);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Families.AnyAsync(f => f.Id == id);
        }
    }
}
