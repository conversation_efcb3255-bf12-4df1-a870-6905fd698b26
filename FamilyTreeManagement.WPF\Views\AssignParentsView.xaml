<UserControl x:Class="FamilyTreeManagement.WPF.Views.AssignParentsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
    </UserControl.Resources>

    <materialDesign:Card>
        <StackPanel Margin="24">
            <TextBlock Text="{Binding Path=DataContext.LocalizationService.AssignParents, RelativeSource={RelativeSource AncestorType=Window}}"
                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                     Margin="0,0,0,24"/>

            <!-- Selected Person -->
            <materialDesign:Card Margin="0,0,0,16">
                <StackPanel Margin="16">
                    <TextBlock Text="{Binding Path=DataContext.LocalizationService.SelectedPerson, RelativeSource={RelativeSource AncestorType=Window}}"
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             Margin="0,0,0,16"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:PackIcon Grid.Column="0"
                                               Kind="Account"
                                               Width="32" Height="32"
                                               VerticalAlignment="Center"
                                               Margin="0,0,16,0"/>

                        <StackPanel Grid.Column="1">
                            <TextBlock Text="{Binding SelectedPerson.FullName}"
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>
                            <TextBlock Text="{Binding SelectedPerson.Age, StringFormat='Age: {0}'}"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Opacity="0.68"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Search Available Persons -->
            <materialDesign:Card Margin="0,0,0,16">
                <StackPanel Margin="16">
                    <TextBlock Text="{Binding Path=DataContext.LocalizationService.SearchAvailablePersons, RelativeSource={RelativeSource AncestorType=Window}}"
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             Margin="0,0,0,16"/>

                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                           materialDesign:HintAssist.Hint="{Binding Path=DataContext.LocalizationService.SearchByName, RelativeSource={RelativeSource AncestorType=Window}}"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           Margin="0,0,0,16">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchPersonsCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <ListBox ItemsSource="{Binding AvailablePersons}"
                           Style="{StaticResource MaterialDesignListBox}"
                           MaxHeight="200">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Grid.Column="0"
                                                           Kind="Account"
                                                           Width="20" Height="20"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,12,0"/>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="{Binding FullName}"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"/>
                                        <TextBlock Text="{Binding Age, StringFormat='Age: {0}'}"
                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                 Opacity="0.68"/>
                                    </StackPanel>

                                    <Button Grid.Column="2"
                                          Content="{Binding Path=DataContext.LocalizationService.SetAsFather, RelativeSource={RelativeSource AncestorType=Window}}"
                                          Command="{Binding DataContext.SetFatherCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                          CommandParameter="{Binding}"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="8,0"/>

                                    <Button Grid.Column="3"
                                          Content="{Binding Path=DataContext.LocalizationService.SetAsMother, RelativeSource={RelativeSource AncestorType=Window}}"
                                          Command="{Binding DataContext.SetMotherCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                          CommandParameter="{Binding}"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="8,0"/>
                                </Grid>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </StackPanel>
            </materialDesign:Card>

            <!-- Selected Parents -->
            <materialDesign:Card Margin="0,0,0,16">
                <StackPanel Margin="16">
                    <TextBlock Text="{Binding Path=DataContext.LocalizationService.SelectedParents, RelativeSource={RelativeSource AncestorType=Window}}"
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             Margin="0,0,0,16"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Father -->
                        <Border Grid.Column="0"
                              BorderBrush="{DynamicResource MaterialDesignDivider}"
                              BorderThickness="1"
                              CornerRadius="4"
                              Padding="12">
                            <StackPanel>
                                <TextBlock Text="{Binding Path=DataContext.LocalizationService.Father, RelativeSource={RelativeSource AncestorType=Window}}"
                                         Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                         Margin="0,0,0,8"/>

                                <StackPanel Visibility="{Binding SelectedFather, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="{Binding SelectedFather.FullName}"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                    <TextBlock Text="{Binding SelectedFather.Age, StringFormat='Age: {0}'}"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Opacity="0.68"/>
                                </StackPanel>

                                <TextBlock Text="{Binding Path=DataContext.LocalizationService.NoFatherSelected, RelativeSource={RelativeSource AncestorType=Window}}"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         Opacity="0.68"
                                         Visibility="{Binding SelectedFather, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </Border>

                        <!-- Mother -->
                        <Border Grid.Column="2"
                              BorderBrush="{DynamicResource MaterialDesignDivider}"
                              BorderThickness="1"
                              CornerRadius="4"
                              Padding="12">
                            <StackPanel>
                                <TextBlock Text="{Binding Path=DataContext.LocalizationService.Mother, RelativeSource={RelativeSource AncestorType=Window}}"
                                         Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                         Margin="0,0,0,8"/>

                                <StackPanel Visibility="{Binding SelectedMother, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="{Binding SelectedMother.FullName}"
                                             Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                    <TextBlock Text="{Binding SelectedMother.Age, StringFormat='Age: {0}'}"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Opacity="0.68"/>
                                </StackPanel>

                                <TextBlock Text="{Binding Path=DataContext.LocalizationService.NoMotherSelected, RelativeSource={RelativeSource AncestorType=Window}}"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         Opacity="0.68"
                                         Visibility="{Binding SelectedMother, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                <Button Content="{Binding Path=DataContext.LocalizationService.ClearSelection, RelativeSource={RelativeSource AncestorType=Window}}"
                      Command="{Binding ClearSelectionCommand}"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="0,0,16,0"/>
                <Button Content="{Binding Path=DataContext.LocalizationService.AssignParents, RelativeSource={RelativeSource AncestorType=Window}}"
                      Command="{Binding AssignParentsCommand}"
                      Style="{StaticResource MaterialDesignRaisedButton}"/>
            </StackPanel>
        </StackPanel>
    </materialDesign:Card>
</UserControl>
