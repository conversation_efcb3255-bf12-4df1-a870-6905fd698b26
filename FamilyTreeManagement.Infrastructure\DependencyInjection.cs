using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FamilyTreeManagement.Domain.Interfaces;
using FamilyTreeManagement.Infrastructure.Data;
using FamilyTreeManagement.Infrastructure.Repositories;

namespace FamilyTreeManagement.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Add DbContext with MySQL
            services.AddDbContext<FamilyTreeContext>(options =>
                options.UseMySql(
                    configuration.GetConnectionString("DefaultConnection"),
                    ServerVersion.AutoDetect(configuration.GetConnectionString("DefaultConnection")),
                    b => b.MigrationsAssembly(typeof(FamilyTreeContext).Assembly.FullName)));

            // Register repositories
            services.AddScoped<IPersonRepository, PersonRepository>();
            services.AddScoped<IPersonRelationRepository, PersonRelationRepository>();
            services.AddScoped<IFamilyRepository, FamilyRepository>();

            return services;
        }
    }
}
