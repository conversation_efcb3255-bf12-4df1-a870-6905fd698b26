using FamilyTreeManagement.Domain.Entities;
using FamilyTreeManagement.Domain.Enums;

namespace FamilyTreeManagement.Domain.Interfaces
{
    public interface IPersonRelationRepository
    {
        Task<IEnumerable<PersonRelation>> GetAllAsync();
        Task<PersonRelation?> GetByIdAsync(int id);
        Task<IEnumerable<PersonRelation>> GetRelationsByPersonAsync(int personId);
        Task<IEnumerable<PersonRelation>> GetRelationsByParentAsync(int parentId);
        Task<IEnumerable<PersonRelation>> GetRelationsByChildAsync(int childId);
        Task<PersonRelation?> GetRelationAsync(int parentId, int childId, RelationType relationType);
        Task<PersonRelation> AddAsync(PersonRelation relation);
        Task<PersonRelation> UpdateAsync(PersonRelation relation);
        Task<bool> DeleteAsync(int id);
        Task<bool> DeleteRelationAsync(int parentId, int childId, RelationType relationType);
        Task<bool> ExistsAsync(int parentId, int childId, RelationType relationType);
    }
}
