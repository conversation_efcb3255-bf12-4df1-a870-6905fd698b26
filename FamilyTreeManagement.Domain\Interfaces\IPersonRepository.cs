using FamilyTreeManagement.Domain.Entities;

namespace FamilyTreeManagement.Domain.Interfaces
{
    public interface IPersonRepository
    {
        Task<IEnumerable<Person>> GetAllAsync();
        Task<Person?> GetByIdAsync(int id);
        Task<Person?> GetByIdWithRelationsAsync(int id);
        Task<IEnumerable<Person>> SearchByNameAsync(string name);
        Task<Person> AddAsync(Person person);
        Task<Person> UpdateAsync(Person person);
        Task<bool> DeleteAsync(int id);
        Task<IEnumerable<Person>> GetChildrenAsync(int parentId);
        Task<IEnumerable<Person>> GetParentsAsync(int childId);
        Task<IEnumerable<Person>> GetSiblingsAsync(int personId);
        Task<bool> ExistsAsync(int id);
    }
}
