# Family Tree Management System

A WPF application for managing family trees with clean architecture, modern UI using Material Design, and MySQL database with recursive table design.

## Features

- **Register Families**: Create new families with father, mother, and children
- **Assign Parents**: Assign parents to existing persons in the system
- **Search Functionality**: Search persons by name
- **Modern UI**: Material Design WPF interface
- **Clean Architecture**: Separated layers (Domain, Application, Infrastructure, Presentation)
- **Recursive Database Design**: Efficient family relationship management

## Architecture

### Project Structure
```
FamilyTreeManagement/
├── FamilyTreeManagement.Domain/          # Core entities and interfaces
├── FamilyTreeManagement.Infrastructure/  # Database implementation (MySQL)
├── FamilyTreeManagement.Application/     # Business logic and services
├── FamilyTreeManagement.WPF/            # Presentation layer (WPF UI)
└── Database/                            # SQL scripts
```

### Database Design

#### Tables:
1. **Persons** - Store individual person data
2. **PersonRelations** - Handle recursive family relationships (parent-child)
3. **Families** - Group family units

#### Recursive Relationship Pattern:
The `PersonRelations` table uses a recursive design where:
- `ParentId` references a person who is a parent
- `ChildId` references a person who is a child
- `RelationType` defines the relationship (Father=1, Mother=2, Son=3, Daughter=4, Spouse=5)

## Prerequisites

- .NET 8.0 SDK
- MySQL Server 8.0 or higher
- Visual Studio 2022 or VS Code

## Setup Instructions

### 1. Database Setup

1. Install MySQL Server if not already installed
2. Create the database using the provided script:
   ```sql
   mysql -u root -p < Database/CreateDatabase.sql
   ```

### 2. Configuration

1. Update the connection string in `FamilyTreeManagement.WPF/appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=localhost;Database=FamilyTreeDB;Uid=root;Pwd=your_password_here;"
     }
   }
   ```

### 3. Build and Run

1. Open the solution in Visual Studio 2022
2. Restore NuGet packages:
   ```bash
   dotnet restore
   ```
3. Build the solution:
   ```bash
   dotnet build
   ```
4. Run the application:
   ```bash
   dotnet run --project FamilyTreeManagement.WPF
   ```

## Usage

### Register a New Family

1. Click "Register Family" button
2. Fill in family information:
   - Family name (required)
   - Father details (optional)
   - Mother details (optional)
   - Marriage information (optional)
3. Add children:
   - Enter child details and click "Add"
   - Remove children if needed
4. Click "Register Family" to save

### Assign Parents to Existing Person

1. Select a person from the person list
2. Click "Assign Parents" button
3. Search and select father and/or mother from available persons
4. Click "Assign Parents" to save relationships

### Search Persons

- Use the search box in the person list to find persons by name
- Press Enter or the search will trigger automatically

## Technologies Used

- **Frontend**: WPF with Material Design
- **Backend**: .NET 8.0
- **Database**: MySQL with Entity Framework Core
- **Architecture**: Clean Architecture pattern
- **MVVM**: CommunityToolkit.Mvvm
- **ORM**: Entity Framework Core with Pomelo MySQL provider

## Database Schema

### Persons Table
```sql
CREATE TABLE Persons (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    FirstName VARCHAR(100) NOT NULL,
    LastName VARCHAR(100) NOT NULL,
    DateOfBirth DATE NULL,
    DateOfDeath DATE NULL,
    Gender INT NOT NULL,
    PlaceOfBirth VARCHAR(200) NULL,
    Occupation VARCHAR(100) NULL,
    Notes TEXT NULL,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NULL
);
```

### PersonRelations Table (Recursive)
```sql
CREATE TABLE PersonRelations (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    ParentId INT NOT NULL,
    ChildId INT NOT NULL,
    RelationType INT NOT NULL,
    CreatedAt DATETIME NOT NULL,
    Notes VARCHAR(500) NULL,
    FOREIGN KEY (ParentId) REFERENCES Persons(Id),
    FOREIGN KEY (ChildId) REFERENCES Persons(Id),
    UNIQUE KEY (ParentId, ChildId, RelationType)
);
```

### Families Table
```sql
CREATE TABLE Families (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    FamilyName VARCHAR(200) NOT NULL,
    FatherId INT NULL,
    MotherId INT NULL,
    MarriageDate DATE NULL,
    MarriagePlace VARCHAR(200) NULL,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NULL,
    Notes TEXT NULL,
    FOREIGN KEY (FatherId) REFERENCES Persons(Id),
    FOREIGN KEY (MotherId) REFERENCES Persons(Id)
);
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.
