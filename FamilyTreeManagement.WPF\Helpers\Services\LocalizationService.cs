using FamilyTreeManagement.WPF.Helpers.Providers;
using FamilyTreeManagement.WPF.Resources;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Windows;

namespace FamilyTreeManagement.WPF.Helpers.Services
{
    public class LocalizationService : INotifyPropertyChanged
    {
        private readonly ILogger<LocalizationService> _logger;
        private readonly IStringLocalizer _localizer;
        //private readonly JsonLocalizationProvider _localizationProvider;
        private bool _isRightToLeft;
        private CultureInfo _currentCulture;

        public LocalizationService(
            IStringLocalizerFactory localizerFactory,
            ILogger<LocalizationService> logger)
        {
            _logger = logger;
            _localizer = localizerFactory.Create("Strings", "FamilyTreeManagement.WPF");
        }

        public bool IsRightToLeft
        {
            get => _isRightToLeft;
            private set
            {
                if (_isRightToLeft != value)
                {
                    _isRightToLeft = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FlowDirection));
                }
            }
        }

        public FlowDirection FlowDirection => IsRightToLeft ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;

        public string this[string key]
        {
            get
            {
                _logger.LogDebug("Getting localized string for key: {key}", key);
                var result = _localizer[key];
                _logger.LogDebug("Localized result for key '{key}': '{result}'", key, result);
                return result ?? $"[{key}]";
            }
        }

        public string this[string key, params object[] arguments]
        {
            get
            {
                var result = _localizer[key, arguments];
                _logger.LogDebug("Localized result for key '{key}' with args: '{result}'", key, result);
                return result ?? $"[{key}]";
            }
        }

        public string GetString(string key) => this[key];

        public string GetString(string key, params object[] arguments) => this[key, arguments];

        public void SetCulture(string cultureCode)
        {
            _currentCulture = new CultureInfo(cultureCode);
            CultureInfo.CurrentCulture = _currentCulture;
            CultureInfo.CurrentUICulture = _currentCulture;

            // Set RTL for Arabic and other RTL languages
            IsRightToLeft = cultureCode.StartsWith("ar") || cultureCode.StartsWith("he") || cultureCode.StartsWith("fa");

            // Notify all bindings that the culture has changed
            OnPropertyChanged(string.Empty);
        }

        public void SetArabic() => SetCulture("ar");
        public void SetEnglish() => SetCulture("en");

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // Commonly used strings as properties for easier binding
        public string AppTitle => GetString("AppTitle");
        public string AddPerson => GetString("AddPerson");
        public string RegisterFamily => GetString("RegisterFamily");
        public string AssignParents => GetString("AssignParents");
        public string Refresh => GetString("Refresh");
        public string SearchPersons => GetString("SearchPersons");
        public string Families => GetString("Families");
        public string AddNewPerson => GetString("AddNewPerson");
        public string FirstName => GetString("FirstName");
        public string LastName => GetString("LastName");
        public string Gender => GetString("Gender");
        public string Male => GetString("Male");
        public string Female => GetString("Female");
        public string Other => GetString("Other");
        public string DateOfBirth => GetString("DateOfBirth");
        public string PlaceOfBirth => GetString("PlaceOfBirth");
        public string Occupation => GetString("Occupation");
        public string Clear => GetString("Clear");
        public string Close => GetString("Close");
        public string Success => GetString("Success");
        public string Error => GetString("Error");
        public string PersonAddedSuccessfully => GetString("PersonAddedSuccessfully");
        public string FormCleared => GetString("FormCleared");
        public string MissingRequiredFields => GetString("MissingRequiredFields");
        public string MissingGender => GetString("MissingGender");

        // Additional properties for the new form
        public string PersonInformation => GetString("PersonInformation");
        public string SelectGender => GetString("SelectGender");

        // Register Family properties
        public string RegisterNewFamily => GetString("RegisterNewFamily");
        public string FamilyInformation => GetString("FamilyInformation");
        public string FatherInformation => GetString("FatherInformation");
        public string MotherInformation => GetString("MotherInformation");
        public string ChildrenInformation => GetString("ChildrenInformation");
        public string FamilyName => GetString("FamilyName");
        public string Father => GetString("Father");
        public string Mother => GetString("Mother");
        public string MarriageDate => GetString("MarriageDate");
        public string MarriagePlace => GetString("MarriagePlace");
        public string Add => GetString("Add");
        public string Remove => GetString("Remove");

        // Assign Parents properties
        public string SelectedPerson => GetString("SelectedPerson");
        public string SelectedParents => GetString("SelectedParents");
        public string SearchAvailablePersons => GetString("SearchAvailablePersons");
        public string SearchByName => GetString("SearchByName");
        public string SetAsFather => GetString("SetAsFather");
        public string SetAsMother => GetString("SetAsMother");
        public string NoFatherSelected => GetString("NoFatherSelected");
        public string NoMotherSelected => GetString("NoMotherSelected");
        public string ClearSelection => GetString("ClearSelection");

        // Helper method to get localized string with fallback
        public string GetLocalizedString(string key, string fallback = "")
        {
            try
            {
                var result = GetString(key);
                return string.IsNullOrEmpty(result) ? fallback : result;
            }
            catch
            {
                return fallback;
            }
        }
    }
}
