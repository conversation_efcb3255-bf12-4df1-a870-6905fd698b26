<UserControl x:Class="FamilyTreeManagement.WPF.Views.RegisterFamilyView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <materialDesign:Card>
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="24">
                <TextBlock Text="Register New Family"
                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                         Margin="0,0,0,24"/>

                <!-- Family Information -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="Family Information"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,0,0,16"/>

                        <TextBox Text="{Binding FamilyName}"
                               materialDesign:HintAssist.Hint="Family Name *"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               Margin="0,0,0,16"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <DatePicker Grid.Column="0"
                                      SelectedDate="{Binding MarriageDate}"
                                      materialDesign:HintAssist.Hint="Marriage Date"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>

                            <TextBox Grid.Column="2"
                                   Text="{Binding MarriagePlace}"
                                   materialDesign:HintAssist.Hint="Marriage Place"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Father Information -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="Father Information"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,0,0,16"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0"
                                   Text="{Binding FatherFirstName}"
                                   materialDesign:HintAssist.Hint="First Name"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"/>

                            <TextBox Grid.Column="2"
                                   Text="{Binding FatherLastName}"
                                   materialDesign:HintAssist.Hint="Last Name"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"/>

                            <DatePicker Grid.Column="4"
                                      SelectedDate="{Binding FatherDateOfBirth}"
                                      materialDesign:HintAssist.Hint="Date of Birth"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Mother Information -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="Mother Information"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,0,0,16"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0"
                                   Text="{Binding MotherFirstName}"
                                   materialDesign:HintAssist.Hint="First Name"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"/>

                            <TextBox Grid.Column="2"
                                   Text="{Binding MotherLastName}"
                                   materialDesign:HintAssist.Hint="Last Name"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"/>

                            <DatePicker Grid.Column="4"
                                      SelectedDate="{Binding MotherDateOfBirth}"
                                      materialDesign:HintAssist.Hint="Date of Birth"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Children Information -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="Children Information"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,0,0,16"/>

                        <!-- Add Child Form -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="8"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="8"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="8"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="8"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0"
                                   Text="{Binding ChildFirstName}"
                                   materialDesign:HintAssist.Hint="First Name"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"/>

                            <TextBox Grid.Column="2"
                                   Text="{Binding ChildLastName}"
                                   materialDesign:HintAssist.Hint="Last Name"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"/>

                            <DatePicker Grid.Column="4"
                                      SelectedDate="{Binding ChildDateOfBirth}"
                                      materialDesign:HintAssist.Hint="Date of Birth"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>

                            <ComboBox Grid.Column="6"
                                    SelectedItem="{Binding SelectedChildGenderItem}"
                                    materialDesign:HintAssist.Hint="Gender"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                <ComboBox.Items>
                                    <ComboBoxItem Content="Male" Tag="1"/>
                                    <ComboBoxItem Content="Female" Tag="2"/>
                                </ComboBox.Items>
                            </ComboBox>

                            <Button Grid.Column="8"
                                  Content="Add"
                                  Command="{Binding AddChildCommand}"
                                  Style="{StaticResource MaterialDesignRaisedButton}"/>
                        </Grid>

                        <!-- Children List -->
                        <ItemsControl ItemsSource="{Binding Children}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <materialDesign:Card Margin="0,4">
                                        <Grid Margin="12">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="{Binding FirstName}"
                                                         Style="{StaticResource MaterialDesignSubtitle2TextBlock}"/>
                                                <TextBlock Text="{Binding LastName}"
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         Opacity="0.68"/>
                                            </StackPanel>

                                            <Button Grid.Column="1"
                                                  Content="Remove"
                                                  Command="{Binding DataContext.RemoveChildCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  CommandParameter="{Binding}"
                                                  Style="{StaticResource MaterialDesignOutlinedButton}"/>
                                        </Grid>
                                    </materialDesign:Card>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="Clear"
                          Command="{Binding ClearFormCommand}"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,16,0"/>
                    <Button Content="Register Family"
                          Command="{Binding RegisterFamilyCommand}"
                          Style="{StaticResource MaterialDesignRaisedButton}"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </materialDesign:Card>
</UserControl>
