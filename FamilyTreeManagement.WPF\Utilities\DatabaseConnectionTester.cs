using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MySqlConnector;
using System.Windows;

namespace FamilyTreeManagement.WPF.Utilities
{
    public static class DatabaseConnectionTester
    {
        public static async Task<bool> TestConnectionAsync(IConfiguration configuration, ILogger? logger = null)
        {
            try
            {
                var connectionString = configuration.GetConnectionString("DefaultConnection");

                // Create a safe version for logging (mask password)
                var safeConnectionString = connectionString;
                if (!string.IsNullOrEmpty(connectionString))
                {
                    var builder = new MySqlConnectionStringBuilder(connectionString);
                    var originalPassword = builder.Password;
                    builder.Password = string.IsNullOrEmpty(originalPassword) ? "" : "***";
                    safeConnectionString = builder.ConnectionString;
                }

                logger?.LogInformation("Testing database connection with connection string: {ConnectionString}", safeConnectionString);

                if (string.IsNullOrEmpty(connectionString))
                {
                    logger?.LogError("Connection string is null or empty");
                    return false;
                }

                logger?.LogInformation("Creating MySqlConnection object...");
                using var connection = new MySqlConnection(connectionString);
                logger?.LogInformation("MySqlConnection object created successfully");

                logger?.LogInformation("Attempting to open database connection...");

                try
                {
                    // Add timeout to prevent hanging indefinitely
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));

                    logger?.LogInformation("About to call connection.OpenAsync()...");
                    await connection.OpenAsync(cts.Token);
                    logger?.LogInformation("connection.OpenAsync() completed successfully");
                }
                catch (OperationCanceledException ex)
                {
                    logger?.LogError(ex, "Database connection timed out after 10 seconds");
                    throw new TimeoutException("Database connection timed out. MySQL server may not be running or accessible.");
                }
                catch (MySqlException ex)
                {
                    logger?.LogError(ex, "MySQL specific error during connection: {ErrorNumber} - {Message}", ex.Number, ex.Message);
                    throw;
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "Unexpected error during database connection: {Type} - {Message}", ex.GetType().Name, ex.Message);
                    throw;
                }

                logger?.LogInformation("Database connection opened successfully");

                // Test a simple query
                using var command = new MySqlCommand("SELECT 1", connection);
                var result = await command.ExecuteScalarAsync();

                logger?.LogInformation("Test query executed successfully, result: {Result}", result);

                return true;
            }
            catch (MySqlException ex)
            {
                logger?.LogError(ex, "MySQL specific error occurred");

                var errorMessage = ex.Number switch
                {
                    1045 => "Access denied. Please check your username and password.",
                    1049 => "Database does not exist. Please create the database first.",
                    2003 => "Cannot connect to MySQL server. Please ensure MySQL is running.",
                    _ => $"MySQL Error {ex.Number}: {ex.Message}"
                };

                MessageBox.Show($"Database Connection Failed\n\n{errorMessage}\n\nFull error: {ex.Message}",
                    "Database Error", MessageBoxButton.OK, MessageBoxImage.Error);

                return false;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "General error occurred while testing database connection");

                MessageBox.Show($"Database Connection Failed\n\n{ex.Message}",
                    "Database Error", MessageBoxButton.OK, MessageBoxImage.Error);

                return false;
            }
        }

        public static async Task<bool> CreateDatabaseIfNotExistsAsync(IConfiguration configuration, ILogger? logger = null)
        {
            try
            {
                var connectionString = configuration.GetConnectionString("DefaultConnection");
                if (string.IsNullOrEmpty(connectionString))
                {
                    logger?.LogError("Connection string is null or empty");
                    return false;
                }

                // Parse connection string to get database name
                var builder = new MySqlConnectionStringBuilder(connectionString);
                var databaseName = builder.Database;

                // Create connection string without database name
                builder.Database = "";
                var connectionStringWithoutDb = builder.ConnectionString;

                logger?.LogInformation("Checking if database '{DatabaseName}' exists...", databaseName);

                using var connection = new MySqlConnection(connectionStringWithoutDb);

                // Add timeout to prevent hanging
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                try
                {
                    await connection.OpenAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    logger?.LogError("Database connection timed out while checking database existence");
                    throw new TimeoutException("Database connection timed out. MySQL server may not be running or accessible.");
                }

                // Check if database exists
                using var checkCommand = new MySqlCommand($"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{databaseName}'", connection);
                var exists = await checkCommand.ExecuteScalarAsync();

                if (exists == null)
                {
                    logger?.LogInformation("Database '{DatabaseName}' does not exist, creating...", databaseName);

                    // Create database
                    using var createCommand = new MySqlCommand($"CREATE DATABASE `{databaseName}`", connection);
                    await createCommand.ExecuteNonQueryAsync();

                    logger?.LogInformation("Database '{DatabaseName}' created successfully", databaseName);
                }
                else
                {
                    logger?.LogInformation("Database '{DatabaseName}' already exists", databaseName);
                }

                return true;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Error creating database");
                return false;
            }
        }
    }
}
